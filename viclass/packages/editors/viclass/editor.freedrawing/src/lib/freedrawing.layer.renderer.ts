import { UnboundedGraphicLayerCtrl } from '@viclass/editor.core';
import { FreedrawingObjCtrl } from './objects/freedrawing.obj.ctrl';
import { PenObjCtrl } from './objects/freedrawing.pen.obj.ctrl';

/**
 *
 * <AUTHOR>
 */
export class FreedrawingLayerRenderer {
    objects: FreedrawingObjCtrl<any>[] = [];
    scheduled: boolean = false;
    private newObjs: FreedrawingObjCtrl<any>[] = [];

    reset: boolean = false;

    constructor(
        public layer: UnboundedGraphicLayerCtrl,
        public isPreviewLayer: boolean = false
    ) {
        layer.attachRenderer(() => this.freedLayerRenderer());
    }

    freedLayerRenderer() {
        const ctx = this.layer.ctx as CanvasRenderingContext2D;
        if (!this.isPreviewLayer) {
            if (this.newObjs.length > 0) {
                this.newObjs.forEach(o => o.render(this.layer.ctx as CanvasRenderingContext2D));
                this.newObjs = [];
            } else {
                ctx.save();

                // Use the identity matrix while clearing the canvas
                ctx.setTransform(1, 0, 0, 1, 0, 0);
                ctx.clearRect(0, 0, this.layer.canvas.width, this.layer.canvas.height);

                // Restore the transform
                ctx.restore();

                this.objects.forEach(o => o.render(this.layer.ctx as CanvasRenderingContext2D, this.isPreviewLayer));
            }
        }

        if (this.isPreviewLayer) {
            const o = this.objects[0];
            if (this.reset || !o || !(o instanceof PenObjCtrl)) {
                console.log('Clear all in preview!');
                // don't clear anything if it is a pen obj
                // clear board if it is not a pen obj

                ctx.save();

                // Use the identity matrix while clearing the canvas
                ctx.setTransform(1, 0, 0, 1, 0, 0);
                ctx.clearRect(0, 0, this.layer.canvas.width, this.layer.canvas.height);

                // Restore the transform
                ctx.restore();
            }

            this.objects.forEach(o => o.render(this.layer.ctx as CanvasRenderingContext2D, this.isPreviewLayer));
        }

        this.reset = false;
    }

    /**
     * Add an object controller to the layer.
     * @param ctrl
     * @param isDrawingOnly whether the object is only for drawing and not actually belongs to the layer.
     * This is used for previewing the pencil stroke on the preview layer to avoid re-rendering the whole layer on
     * every point update. The pencil obj ctrl should be added to the layer it belongs to as well. When ending the
     * preview, the preview layer will be removed and the layer actually containing the pencil stroke will be re-rendered.
     */
    addObjCtrl(ctrl: FreedrawingObjCtrl<any>, isDrawingOnly: boolean = false) {
        this.objects.push(ctrl);
        if (!isDrawingOnly) ctrl.layer = this.layer;
    }

    removeObjCtrl(objId: number, destroy: boolean = true) {
        this.objects = this.objects.filter(o => {
            if (o.id == objId) {
                if (destroy) o.destroy();
                return false;
            }

            return true;
        });
    }

    scheduleRender(newObj?: FreedrawingObjCtrl<any>) {
        if (newObj) this.newObjs.push(newObj);
        else this.newObjs = []; // if has newobj,we only draw the new obj, otherwise, someone requested full redraw

        if (this.scheduled) return;

        this.scheduled = true;
        requestAnimationFrame(() => {
            this.layer.renderer(this.layer.viewport, this.layer);
            this.scheduled = false;
        });
    }

    resetDrawing() {
        this.reset = true;
        this.scheduleRender();
    }

    hasObjects() {
        return this.objects.length > 0;
    }

    hasObjectId(objId: number) {
        return this.objects.find(o => o.id == objId) != null;
    }

    getObjCtrl(objId: number) {
        return this.objects.find(o => o.id == objId);
    }

    onRemoved(): void {
        for (const obj of this.objects) {
            obj.destroy();
        }
    }
}
