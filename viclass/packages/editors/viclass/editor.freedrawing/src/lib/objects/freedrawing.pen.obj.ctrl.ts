import { DocumentEditor, Position, Rectangle, VDocCtrl } from '@viclass/editor.core';
import { getStroke } from 'perfect-freehand';
import { PenObj } from '../freedrawing.models';
import { FreedrawingObjCtrl } from './freedrawing.obj.ctrl';

function average(a: number, b: number) {
    return (a + b) / 2;
}

/**
 *
 * <AUTHOR>
 */
export class PenObjCtrl extends FreedrawingObjCtrl<PenObj> {
    localPoints: Position[] = []; // local positions
    previewingPoints: Position[] | undefined = undefined;

    private _combineBoundary: Rectangle = null;
    private path?: Path2D;
    private numNewPreview: number = 0;

    override get boundary(): Rectangle {
        return this._combineBoundary;
    }

    constructor(state: PenObj, editor: DocumentEditor, doc: VDocCtrl) {
        super(state, editor, doc);
        this.updateCombineBoundary();
    }

    validate(): boolean {
        return this.state.points.length > 0;
    }

    override onStateChange() {
        this.prepare();
        this.path = undefined;

        this.updateCombineBoundary();
    }

    /**
     * Updates the combined boundary of the pen object.
     *
     * !!! this only count the points in the state, not the previewing points or the stroke generated from it.
     * As currently the boundary only use for fit the fred doc into view, so precise calculation is not required.
     */
    private updateCombineBoundary() {
        if (!this.state.points || this.state.points.length === 0) {
            this._combineBoundary = null;
            return;
        }

        this._combineBoundary = this.state.points.reduce(
            (acc, p) => {
                if (!acc) {
                    return { start: p, end: p };
                }
                acc.start = {
                    x: Math.min(acc.start.x, p.x),
                    y: Math.min(acc.start.y, p.y),
                };
                acc.end = {
                    x: Math.max(acc.end.x, p.x),
                    y: Math.max(acc.end.y, p.y),
                };
                return acc;
            },
            <Rectangle>{
                start: { x: Infinity, y: Infinity },
                end: { x: -Infinity, y: -Infinity },
            }
        );
    }

    override prepare() {}

    getSvgPathFromStroke(points, closed = true) {
        const len = points.length;

        if (len < 4) {
            return ``;
        }

        let a = points[0];
        let b = points[1];
        const c = points[2];

        let result = `M${a[0].toFixed(2)},${a[1].toFixed(2)} Q${b[0].toFixed(
            2
        )},${b[1].toFixed(2)} ${average(b[0], c[0]).toFixed(2)},${average(b[1], c[1]).toFixed(2)} T`;

        for (let i = 2, max = len - 1; i < max; i++) {
            a = points[i];
            b = points[i + 1];
            result += `${average(a[0], b[0]).toFixed(2)},${average(a[1], b[1]).toFixed(2)} `;
        }

        if (closed) {
            result += 'Z';
        }

        return result;
    }

    render(ctx: CanvasRenderingContext2D, preview = false) {
        if (!this.validate()) return;

        if (!preview) {
            this.previewingPoints = undefined; // if it is no longer a preview
            this.numNewPreview = 0;
            ctx.save();

            if (!this.path) {
                const allPoints = [...this.state.points];
                const numPoint = allPoints.length;
                const stroke = getStroke(allPoints, {
                    size: this.state.toolState.lineWidth,
                    start: {
                        taper: false,
                    },
                    end: {
                        taper: numPoint < 10 ? false : 10,
                    },
                    thinning: numPoint < 10 ? 0.0 : 0.5,
                    streamline: 0.6,
                    smoothing: 1,
                    last: true,
                });

                stroke.forEach(p => {
                    const lp = this.layer.canvasPos({ x: p[0], y: p[1] });
                    p[0] = lp.x;
                    p[1] = lp.y;
                });
                const pathData = this.getSvgPathFromStroke(stroke);
                this.path = new Path2D(pathData);
            }
            ctx.fillStyle = this.state.toolState.stroke;
            ctx.fill(this.path);
            ctx.restore();
        } else {
            if (!this.previewingPoints || this.previewingPoints.length == 0) return;

            ctx.save();
            ctx.beginPath();

            ctx.lineWidth = this.state.toolState.lineWidth;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = this.state.toolState.stroke;
            ctx.globalAlpha = 0.2;

            ctx.moveTo(this.previewingPoints[0].x, this.previewingPoints[0].y);

            for (let i = 0; i < this.previewingPoints.length; i++) {
                ctx.lineTo(this.previewingPoints[i].x, this.previewingPoints[i].y);
            }
            ctx.stroke();

            ctx.closePath();
            ctx.restore();

            this.numNewPreview = 1;
        }
    }

    updatePartial(partial: PenObj) {
        this.state.points = [...this.state.points, ...partial.points];
        this.state.toolState = partial.toolState;
        // clear all preview points
        this.previewingPoints = [partial.points[partial.points.length - 1]];
        this.numNewPreview = 1;

        this.onStateChange();
    }

    updatePreview(point: Position) {
        if (!this.previewingPoints) this.previewingPoints = [];
        this.previewingPoints.push(point);
        this.numNewPreview++;
    }
}
