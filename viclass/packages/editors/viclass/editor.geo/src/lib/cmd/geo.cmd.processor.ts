﻿import { interpolate, InterpolationHandler } from '@viclass/editor.coordinator/common';
import {
    AbstractCommand,
    Cmd,
    CmdMeta,
    CmdOriginType,
    CmdProcessor,
    ErrorHandlerDecorator,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    ViewportManager,
} from '@viclass/editor.core';
import { CmdTypeProto, GeoKind } from '@viclass/proto/editor.geo';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { DocRenderProp, GeoRenderElement } from '../model';
import { GeoDoc, GeoDocInitData, GeoLayer } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { Geo2dRenderer, GeoRenderer } from '../renderer';
import { geoDocReg, geoLayerReg } from '../tools/tool.utils';
import {
    EndPreviewCmd,
    HighlightElementsCmd,
    PotentialSelectionElementsCmd,
    PreviewAngleCmd,
    PreviewCircleShapeCmd,
    PreviewEllipseShapeCmd,
    PreviewLineCmd,
    PreviewPolygonCmd,
    PreviewSectorShapeCmd,
    RemoveHighlightElementsCmd,
    RemovePotentialSelectionElementsCmd,
    RemovePreviewElsCmd,
    RenderAngleCmd,
    RenderCircleCmd,
    RenderCircleShapeCmd,
    RenderEllipseCmd,
    RenderEllipseShapeCmd,
    RenderLineCmd,
    RenderPolygonCmd,
    RenderSectorCmd,
    RenderSectorShapeCmd,
    RenderVertexCmd,
    UpdateDocDefaultElRenderPropsCmd,
    UpdateDocStateCmd,
    UpdateElementNameCmd,
    UpdateElementsPropCmd,
} from './geo.cmd';
import {
    convertProtoToDocDefaultElRenderProps,
    convertProtoToDocRenderProp,
    convertProtoToPreviewAngle,
    convertProtoToPreviewCircleShape,
    convertProtoToPreviewLine,
    convertProtoToPreviewPolygon,
    convertProtoToRenderAngle,
    convertProtoToRenderCircle,
    convertProtoToRenderCircleShape,
    convertProtoToRenderEllipse,
    convertProtoToRenderEllipseShape,
    convertProtoToRenderLine,
    convertProtoToRenderPolygon,
    convertProtoToRenderSector,
    convertProtoToRenderSectorShape,
    convertProtoToRenderVertex,
    convertProtoToSettingProperties,
} from './proto.utils';

/**
 * Processes commands related to the GeometryEditor.
 * This class is responsible for handling various command types,
 * updating the editor state, and interacting with the renderer.
 */
export class GeometryCmdProcessor extends CmdProcessor {
    /**
     * Handler for interpolating document state updates.
     * This is used to smoothly animate changes to document properties like zoom and pan.
     */
    private updateDocInterpolationHandler: InterpolationHandler<DocRenderProp> = new InterpolationHandler();

    /**
     * Creates an instance of GeometryCmdProcessor.
     * @param editor The GeometryEditor instance.
     */
    constructor(private editor: GeometryEditor) {
        super();
    }

    /**
     * Processes an incoming command.
     * This method acts as a dispatcher, routing the command to the appropriate handler based on its type.
     * @param cmd The command to process.
     * @returns A promise that resolves with the processed command.
     */
    async processCmd(
        cmd: AbstractCommand<CmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<CmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case FCCmdTypeProto.INSERT_DOC: {
                return this.processInsertDocCmd(cmd as FCInsertDocCmd);
            }
            case FCCmdTypeProto.REMOVE_DOC: {
                await this.processRemoveDocCmd(cmd as FCRemoveDocCmd);
                break;
            }
            case FCCmdTypeProto.INSERT_LAYER: {
                this.processInsertLayerCmd(cmd as FCInsertLayerCmd);
                break;
            }
            case FCCmdTypeProto.PREVIEW_BOUNDARY: {
                this.processNewDocBoundaryCmd(cmd as FCPreviewBoundaryCmd);
                break;
            }
            case FCCmdTypeProto.RELOAD_DOC: {
                return this.processReloadCmd(cmd as FCReloadDocCmd);
            }
            case FCCmdTypeProto.UPDATE_BOUNDARY: {
                return this.processUpdateBoundaryCmd(cmd as FCUpdateDocCmd);
            }
            case CmdTypeProto.RENDER_VERTEX: {
                this.processRenderVertex(cmd as RenderVertexCmd);
                break;
            }
            case CmdTypeProto.RENDER_LINE: {
                this.processRenderLine(cmd as RenderLineCmd);
                break;
            }
            case CmdTypeProto.PREVIEW_LINE: {
                this.processPreviewLine(cmd as PreviewLineCmd);
                break;
            }
            case CmdTypeProto.RENDER_POLYGON: {
                this.processRenderPolygon(cmd as RenderPolygonCmd);
                break;
            }
            case CmdTypeProto.PREVIEW_POLYGON: {
                this.processPreviewPolygon(cmd as PreviewPolygonCmd);
                break;
            }
            case CmdTypeProto.RENDER_CIRCLE_SHAPE: {
                this.processRenderCircleShape(cmd as RenderCircleShapeCmd);
                break;
            }
            case CmdTypeProto.PREVIEW_CIRCLE_SHAPE: {
                this.processPreviewCircleShape(cmd as PreviewCircleShapeCmd);
                break;
            }
            case CmdTypeProto.RENDER_CIRCULAR_SECTOR_SHAPE: {
                this.processRenderSectorShape(cmd as RenderSectorShapeCmd);
                break;
            }
            case CmdTypeProto.PREVIEW_CIRCULAR_SECTOR_SHAPE: {
                this.processPreviewSectorShape(cmd as PreviewSectorShapeCmd);
                break;
            }
            case CmdTypeProto.RENDER_SECTOR: {
                this.processRenderSector(cmd as RenderSectorCmd);
                break;
            }
            case CmdTypeProto.RENDER_ELLIPSE: {
                this.processRenderEllipse(cmd as RenderEllipseCmd);
                break;
            }
            case CmdTypeProto.RENDER_CIRCLE: {
                this.processRenderCircle(cmd as RenderCircleCmd);
                break;
            }
            case CmdTypeProto.RENDER_ELLIPSE_SHAPE: {
                this.processRenderEllipseShape(cmd as RenderEllipseShapeCmd);
                break;
            }
            case CmdTypeProto.PREVIEW_ELLIPSE_SHAPE: {
                this.processPreviewEllipseShape(cmd as PreviewEllipseShapeCmd);
                break;
            }
            case CmdTypeProto.RENDER_ANGLE: {
                this.processRenderAngle(cmd as RenderAngleCmd);
                break;
            }
            case CmdTypeProto.PREVIEW_ANGLE: {
                this.processPreviewAngle(cmd as PreviewAngleCmd);
                break;
            }
            case CmdTypeProto.END_PREVIEW: {
                this.endPreviewMode(cmd as EndPreviewCmd);
                break;
            }
            case CmdTypeProto.HIGHLIGHT_ELEMENTS: {
                this.processHighlightElementsCmd(cmd as HighlightElementsCmd);
                break;
            }
            case CmdTypeProto.REMOVE_HIGHLIGHT_ELEMENTS: {
                this.processRemoveHighlightElementsCmd(cmd as RemoveHighlightElementsCmd);
                break;
            }
            case CmdTypeProto.UPDATE_ELS_PROP: {
                this.processUpdateElsPropCmd(cmd as UpdateElementsPropCmd);
                break;
            }
            case CmdTypeProto.UPDATE_EL_NAME: {
                this.processUpdateElNameCmd(cmd as UpdateElementNameCmd);
                break;
            }
            case CmdTypeProto.UPDATE_DOC_STATE: {
                this.processUpdateDocStateCmd(cmd as UpdateDocStateCmd);
                break;
            }
            case CmdTypeProto.UPDATE_DEFAULT_EL_RENDER_PROPS: {
                this.processUpdateDocDefaultElRenderProps(cmd as UpdateDocDefaultElRenderPropsCmd);
                break;
            }
            case CmdTypeProto.REMOVE_POTENTIAL_SELECTION_ELEMENTS: {
                this.processRemovePotentialSelectionElementsCmd(cmd as RemovePotentialSelectionElementsCmd);
                break;
            }
            case CmdTypeProto.POTENTIAL_SELECTION_ELEMENTS: {
                this.procesPotentialSelectionElementsCmd(cmd as PotentialSelectionElementsCmd);
                break;
            }
            case CmdTypeProto.REMOVE_PREVIEW_BY_REL_IDS: {
                this.processRemovePreviewElsCmd(cmd as RemovePreviewElsCmd);
                break;
            }
            default:
                // If the command type is not recognized, do nothing.
                break;
        }

        return cmd;
    }

    /**
     * Processes a command to reload documents.
     * @param cmd The FCReloadDocCmd command.
     * @returns A promise that resolves with the processed command.
     */
    async processReloadCmd(cmd: FCReloadDocCmd): Promise<FCReloadDocCmd> {
        const promises = cmd.state
            .getLocalidList()
            .map(localId => this.editor.crdFeature.reloadLocalDoc(this.editor, cmd.meta.viewport.id, localId));
        await Promise.all(promises);

        this.editor.insertDocDelegator.notifyDocCreation(cmd.meta.viewport, ...cmd.state.getLocalidList());

        return cmd;
    }

    private procesPotentialSelectionElementsCmd(cmd: PotentialSelectionElementsCmd) {
        const vpId = cmd.meta.viewport.id;
        const layerId = cmd.state.getLayerId();
        const docId = cmd.meta.versionable;
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(vpId, docId));

        const renderer = layerRegistry.getEntity(layerId);
        if (renderer) {
            renderer.addPotentialSelection(cmd.state.getRelIndexList());
            renderer.scheduleRender();
        }
    }

    private processRemovePotentialSelectionElementsCmd(cmd: RemovePotentialSelectionElementsCmd) {
        const vpId = cmd.meta.viewport.id;
        const layerId = cmd.state.getLayerId();
        const docId = cmd.meta.versionable;
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(vpId, docId));

        const renderer = layerRegistry.getEntity(layerId);
        if (renderer) {
            renderer.removePotentialSelection(cmd.state.getRelIndexList());
            renderer.scheduleRender();
        }
    }

    /**
     * Processes a command to highlight elements.
     * @param cmd The HighlightElementsCmd command.
     */
    private processHighlightElementsCmd(cmd: HighlightElementsCmd) {
        const vpId = cmd.meta.viewport.id;
        const layerId = cmd.state.getLayerId();
        const docId = cmd.meta.versionable;
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(vpId, docId));

        const renderer = layerRegistry.getEntity(layerId);
        if (renderer) {
            renderer.highlight(cmd.state.getRelIndexList());
            renderer.scheduleRender();
        }
    }

    /**
     * Processes a command to remove highlighting from elements.
     * @param cmd The RemoveHighlightElementsCmd command.
     */
    private processRemoveHighlightElementsCmd(cmd: RemoveHighlightElementsCmd) {
        const vpId = cmd.meta.viewport.id;
        const layerId = cmd.state.getLayerId();
        const docId = cmd.meta.versionable;
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(vpId, docId));

        const renderer = layerRegistry.getEntity(layerId);
        if (renderer) {
            renderer.removeHighlight(cmd.state.getRelIndexList());
            renderer.scheduleRender();
        }
    }

    /**
     * Processes a command to update an element's name.
     * @param cmd The UpdateElementNameCmd command.
     */
    private processUpdateElNameCmd(cmd: UpdateElementNameCmd) {
        const docId = cmd.meta.versionable;
        const docCtrl = this.getGeoDoc(cmd.meta.viewport, docId);
        const renderer = docCtrl.rendererCtrl;
        if (renderer) {
            const el = renderer.elementAt(cmd.state.getRelIndex());
            el.name = cmd.state.getName();
            // Re-render the layer to reflect the name change.
            renderer.scheduleRender();
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Processes a command to update properties of multiple elements.
     * @param cmd The UpdateElementsPropCmd command.
     */
    private processUpdateElsPropCmd(cmd: UpdateElementsPropCmd) {
        const docId = cmd.meta.versionable;
        const docCtrl = this.getGeoDoc(cmd.meta.viewport, docId);
        const renderer = docCtrl.rendererCtrl;
        const elRenderPropsProto = cmd.state.hasElRenderProps() ? cmd.state.getElRenderProps() : null;

        if (renderer && elRenderPropsProto) {
            const newProps = convertProtoToSettingProperties(elRenderPropsProto);
            cmd.state.getRelIndexList().forEach(relIdx => {
                const rel = renderer.elementAt(relIdx);
                if (rel) {
                    // Apply each property from the proto to the element's render properties.
                    for (const [key, value] of Object.entries(newProps)) {
                        rel.renderProp[key] = value;
                    }
                    // Update the object in the renderer's collection.
                    renderer.addActualElement(rel);
                }
            });
            // Re-render the layer to reflect the property changes.
            renderer.scheduleRender();
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Processes a command to update the document's render state (e.g., zoom, pan).
     * Uses interpolation for smooth transitions if the command is not local.
     * @param cmd The UpdateDocStateCmd command.
     */
    private processUpdateDocStateCmd(cmd: UpdateDocStateCmd) {
        const docId = cmd.meta.versionable;
        const docCtrl = this.getGeoDoc(cmd.meta.viewport, docId);
        const renderer = docCtrl.rendererCtrl;
        // Merge current docRenderProp with incoming changes.
        const docRenderProp = {
            ...docCtrl.state.docRenderProp,
            ...convertProtoToDocRenderProp(cmd.state.getDocRenderProp()),
        } as DocRenderProp;

        if (this.isLocalCmd(cmd)) {
            // Apply changes directly for local commands.
            docCtrl.updateDocRenderProp(docRenderProp);
            if (renderer) renderer.scheduleRender();
        } else {
            // Use interpolation for remote commands to provide a smoother visual experience.
            this.updateDocInterpolationHandler.runInterpolation(
                docRenderProp,
                (preState, curState, ratio) =>
                    // Interpolate specific properties.
                    <DocRenderProp>{
                        ...preState, // Spread existing properties
                        screenUnit: interpolate(preState.screenUnit, curState.screenUnit, ratio),
                        canvasWidth: interpolate(preState.canvasWidth, curState.canvasWidth, ratio),
                        canvasHeight: interpolate(preState.canvasHeight, curState.canvasHeight, ratio),
                        scale: interpolate(preState.scale, curState.scale, ratio),
                        translation: [
                            interpolate(preState.translation[0], curState.translation[0], ratio),
                            interpolate(preState.translation[1], curState.translation[1], ratio),
                        ],
                        rotation: [
                            interpolate(preState.rotation[0], curState.rotation[0], ratio),
                            interpolate(preState.rotation[1], curState.rotation[1], ratio),
                        ],
                    },
                state => {
                    // Callback to apply the interpolated state.
                    docCtrl.updateDocRenderProp(state);
                    if (renderer) renderer.scheduleRender();
                },
                () => structuredClone(docCtrl.state.docRenderProp), // Function to get the current state for interpolation.
                GeometryEditor.cmdChannelThrottle // Throttle time for interpolation.
            );
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Processes a command to update the document's default element render properties.
     * @param cmd The UpdateDocDefaultElRenderPropsCmd command.
     */
    private processUpdateDocDefaultElRenderProps(cmd: UpdateDocDefaultElRenderPropsCmd) {
        const docId = cmd.meta.versionable;
        const docCtrl = this.getGeoDoc(cmd.meta.viewport, docId);
        docCtrl.updateDocDefaultElRenderProps(
            convertProtoToDocDefaultElRenderProps(cmd.state.getDocDefaultElRenderProps())
        );

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Ends the preview mode for a given layer.
     * @param cmd The EndPreviewCmd command.
     */
    endPreviewMode(cmd: EndPreviewCmd) {
        const vpId = cmd.meta.viewport.id;
        const layerId = cmd.state.getLayerId();
        const docId = cmd.meta.versionable;
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(vpId, docId));
        const renderer = layerRegistry.getEntity(layerId);

        renderer.clearPreview();
        renderer.scheduleRender();
    }

    /**
     * Processes rendering of preview elements.
     * Initializes preview mode if not already active and adds elements to the renderer.
     * @param meta The command metadata.
     * @param layerId The ID of the layer to render on.
     * @param rels The geometric elements to render in preview mode.
     */
    processRenderPreviewElements(meta: CmdMeta, layerId: number, rels: GeoRenderElement[]) {
        const vpId = meta.viewport.id;
        const docId = meta.versionable;
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(vpId, docId));
        const renderer = layerRegistry.getEntity(layerId);

        rels.forEach(rel => this.addPreviewElementRecursively(rel, renderer));
        renderer.scheduleRender();
    }

    processRemovePreviewElsCmd(cmd: RemovePreviewElsCmd) {
        const vpId = cmd.meta.viewport.id;
        const docId = cmd.meta.versionable;
        const docRegistry = this.editor.regMan.registry<GeoDocCtrl>(geoDocReg(vpId));
        const docCtrl = docRegistry.getEntity(docId);
        const renderer = docCtrl.rendererCtrl as Geo2dRenderer;

        // Remove preview elements by their relative indices.
        renderer.removePreviewByIds(cmd.state.getRelIdsList());
        renderer.scheduleRender();
    }

    private addPreviewElementRecursively(el: GeoRenderElement, renderer: GeoRenderer) {
        renderer.addPreviewElement(el);
        if (el.pInfo?.refPEl?.length > 0) {
            el.pInfo.refPEl.forEach(subEl => this.addPreviewElementRecursively(subEl, renderer));
        }
    }
    /**
     * Processes rendering of final elements.
     * Stops preview mode if active and adds elements to the renderer.
     * @param meta The command metadata.
     * @param layerId The ID of the layer to render on.
     * @param rels The geometric elements to render.
     */
    processRenderElements(meta: CmdMeta, layerId: number, rels: GeoRenderElement[]) {
        const vpId = meta.viewport.id;
        const docId = meta.versionable;
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(vpId, docId));
        const renderer = layerRegistry.getEntity(layerId);

        rels.forEach(rel => {
            // For now, distinction between render element and preview element is in
            // their id.
            if (rel.relIndex >= 0) {
                renderer.addActualElement(rel);
            } else {
                this.addPreviewElementRecursively(rel, renderer);
            }
        });

        renderer.scheduleRender();

        this.clearCurrentViewportHistoryIfRemoteCmd(meta);
    }

    /**
     * Processes a command to render an angle.
     * @param cmd The RenderAngleCmd command.
     */
    processRenderAngle(cmd: RenderAngleCmd) {
        const angle = convertProtoToRenderAngle(cmd.state.getAngle());
        const layerId = cmd.state.getLayerId();

        this.processRenderElements(cmd.meta, layerId, [angle]);
    }

    /**
     * Processes a command to preview an angle.
     * This involves creating preview vertices and lines that form the angle.
     * @param cmd The PreviewAngleCmd command.
     */
    processPreviewAngle(cmd: PreviewAngleCmd) {
        const angle = convertProtoToPreviewAngle(cmd.state.getAngle());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [angle]);
    }

    processPreviewAngleByThreePoints(cmd: PreviewAngleCmd) {
        const angle = convertProtoToPreviewAngle(cmd.state.getAngle());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [angle]);
    }

    /**
     * Processes a command to render a vertex.
     * @param cmd The RenderVertexCmd command.
     */
    processRenderVertex(cmd: RenderVertexCmd) {
        const vertex = convertProtoToRenderVertex(cmd.state.getVertex());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [vertex]);
    }

    /**
     * Processes a command to render a line.
     * @param cmd The RenderLineCmd command.
     */
    processRenderLine(cmd: RenderLineCmd) {
        const line = convertProtoToRenderLine(cmd.state.getLine());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [line]);
    }

    /**
     * Processes a command to preview a line.
     * This involves creating preview vertices for the start and end points of the line.
     * @param cmd The PreviewLineCmd command.
     */
    processPreviewLine(cmd: PreviewLineCmd) {
        const line = convertProtoToPreviewLine(cmd.state.getLine());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [line]);
    }

    /**
     * Processes a command to render a circle shape.
     * @param cmd The RenderCircleShapeCmd command.
     */
    processRenderCircleShape(cmd: RenderCircleShapeCmd) {
        const circle = convertProtoToRenderCircleShape(cmd.state.getCircle());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [circle]);
    }

    /**
     * Processes a command to preview a circle shape.
     * This involves creating a preview vertex for the center and the circle's arc.
     * @param cmd The PreviewCircleShapeCmd command.
     */
    processPreviewCircleShape(cmd: PreviewCircleShapeCmd) {
        const circle = convertProtoToPreviewCircleShape(cmd.state.getCircle());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [circle]);
    }

    /**
     * Processes a command to render a sector (arc).
     * @param cmd The RenderSectorCmd command.
     */
    processRenderSector(cmd: RenderSectorCmd) {
        const arc = convertProtoToRenderSector(cmd.state.getArc());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [arc]);
    }

    /**
     * Processes a command to render an ellipse.
     * @param cmd The RenderEllipseCmd command.
     */
    processRenderEllipse(cmd: RenderEllipseCmd) {
        const arc = convertProtoToRenderEllipse(cmd.state.getArc());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [arc]);
    }

    /**
     * Processes a command to render a circle (arc/outline).
     * @param cmd The RenderCircleCmd command.
     */
    processRenderCircle(cmd: RenderCircleCmd) {
        const arc = convertProtoToRenderCircle(cmd.state.getArc());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [arc]);
    }

    /**
     * Processes a command to render a sector shape (filled area).
     * @param cmd The RenderSectorShapeCmd command.
     */
    processRenderSectorShape(cmd: RenderSectorShapeCmd) {
        const circularSector = convertProtoToRenderSectorShape(cmd.state.getSector());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [circularSector]);
    }

    /**
     * Processes a command to preview a sector shape.
     * This involves creating preview vertices for center, start, and end points,
     * preview lines for the radii, a preview circle for the arc's path, and the sector arc itself.
     * @param cmd The PreviewSectorShapeCmd command.
     */
    processPreviewSectorShape(cmd: PreviewSectorShapeCmd) {}

    /**
     * Processes a command to render an ellipse shape.
     * @param cmd The RenderEllipseShapeCmd command.
     */
    processRenderEllipseShape(cmd: RenderEllipseShapeCmd) {
        const ellipse = convertProtoToRenderEllipseShape(cmd.state.getEllipse());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [ellipse]);
    }

    /**
     * Processes a command to preview an ellipse shape.
     * This involves creating preview vertices for the foci, the ellipse arc, and the filled shape.
     * @param cmd The PreviewEllipseShapeCmd command.
     */
    processPreviewEllipseShape(cmd: PreviewEllipseShapeCmd) {}

    /**
     * Processes a command to render a polygon.
     * @param cmd The RenderPolygonCmd command.
     */
    processRenderPolygon(cmd: RenderPolygonCmd) {
        const polygon = convertProtoToRenderPolygon(cmd.state.getPolygon());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [polygon]);
    }

    /**
     * Processes a command to preview a polygon.
     * This involves creating preview vertices for each face and preview lines for the edges.
     * @param cmd The PreviewPolygonCmd command.
     */
    processPreviewPolygon(cmd: PreviewPolygonCmd) {
        const polygon = convertProtoToPreviewPolygon(cmd.state.getPolygon());
        const layerId = cmd.state.getLayerId();
        this.processRenderElements(cmd.meta, layerId, [polygon]);
    }

    /**
     * Retrieves a GeoDocCtrl instance by its local ID from the viewport's registry.
     * @param vm The ViewportManager instance.
     * @param localId The local ID of the document.
     * @returns The GeoDocCtrl instance, or undefined if not found.
     */
    private getGeoDoc(vm: ViewportManager, localId: number): GeoDocCtrl {
        return this.editor.regMan.registry<GeoDocCtrl>(geoDocReg(vm.id))?.getEntity(localId);
    }

    /**
     * Processes a command to preview a new document's boundary.
     * Delegates to the editor's CRD (Create/Read/Delete) feature.
     * @param cmd The FCPreviewBoundaryCmd command.
     * @returns The processed command.
     */
    private processNewDocBoundaryCmd(cmd: FCPreviewBoundaryCmd): FCPreviewBoundaryCmd {
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);

        return this.editor.crdFeature.processDocCreationPreviewCmd(cmd, this.editor);
    }

    /**
     * Processes a command to insert a new document.
     * If the command is local, it first creates the document via the gateway.
     * Then, it creates a GeoDoc instance and adds it to the editor's document registry.
     * @param cmd The FCInsertDocCmd command.
     * @returns A promise that resolves with the processed command.
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async processInsertDocCmd(cmd: FCInsertDocCmd) {
        const decoder = new TextDecoder();
        const initData: GeoDocInitData = JSON.parse(decoder.decode(cmd.state.getInitdata() as Uint8Array));
        let w = 200; // Default width
        let h = 200; // Default height

        if (initData.boundary) {
            w = initData.boundary.width;
            h = initData.boundary.height;
        }

        let globalId;

        // If the command originated locally, create the document on the backend.
        if (cmd.meta.origin === CmdOriginType.local) {
            const doc = await this.editor.geoGateway.createDoc(
                initData.numDim,
                w,
                h,
                initData.docRenderProp.screenUnit
            );
            globalId = doc.docId;
            initData.docRenderProp = doc.docRenderProp; // Use render prop from backend response
            cmd.state.setGlobalId(globalId);
        } else {
            globalId = cmd.state.getGlobalId();
        }

        const geoDoc = new GeoDoc(globalId, cmd.meta.targetId, initData.kind, initData.docRenderProp);
        this.editor.insertDocDelegator.insertDocCtrl(cmd.meta.viewport, geoDoc);

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);

        return cmd;
    }

    /**
     * Processes a command to remove a document.
     * Iterates through the list of document IDs and removes each one internally.
     * @param cmd The FCRemoveDocCmd command.
     */
    private async processRemoveDocCmd(cmd: FCRemoveDocCmd) {
        const l = cmd.state.getIdsList();
        for (const id of l) {
            await this.editor.internalRemoveDoc(cmd.meta.viewport.id, id.getLocalId());
        }

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    /**
     * Processes a command to insert a new layer into a document.
     * Creates a GeoLayer instance and attaches a renderer to it.
     * @param cmd The FCInsertLayerCmd command.
     */
    private processInsertLayerCmd(cmd: FCInsertLayerCmd) {
        const layerId = cmd.meta.targetId;
        const docId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;
        const zindex = cmd.state.getZIndex();

        const docRegistry = this.editor.regMan.registry<GeoDocCtrl>(geoDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(docId);

        const layerState = new GeoLayer(layerId, fcConvertProtoToBoundary(cmd.state.getBoundary()), zindex);

        // Request the layer from the viewport, which handles actual placement.
        const layerCtrl = this.editor.requestLayer(docCtrl.viewport, true, {
            boundary: layerState.boundary,
            docLocalId: docId,
            docGlobalId: docCtrl.state.globalId,
            viewport: docCtrl.viewport,
            editor: this.editor,
            state: layerState,
        });

        layerCtrl.doc = docCtrl; // Link layer to its document controller.
        layerCtrl.zindex = zindex;

        // Create and attach the appropriate renderer (e.g., Geo2dRenderer).
        const rendererCtrl =
            (docCtrl.state as GeoDoc).kind === GeoKind.GEO2D ? new Geo2dRenderer(layerCtrl, docCtrl) : undefined;
        docCtrl.attachRenderer(rendererCtrl);

        // Register the renderer in the layer registry.
        const layerRegistry = this.editor.regMan.registry<GeoRenderer>(geoLayerReg(cmd.meta.viewport.id, docId));
        layerRegistry.addEntity(layerId, rendererCtrl);

        // Notify about the document creation/update.
        this.editor.insertDocDelegator.notifyDocCreation(vm, docId);
    }

    /**
     * Processes a command to update a document's boundary.
     * Delegates to the editor's boundary delegator.
     * @param cmd The FCUpdateDocCmd command.
     * @returns A promise that resolves with the processed command.
     */
    private async processUpdateBoundaryCmd(cmd: FCUpdateDocCmd) {
        this.editor.boundaryDelegator.processUpdateBoundaryCmd(cmd, GeometryEditor.cmdChannelThrottle);

        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);

        return cmd;
    }

    /**
     * Checks if a command originated locally.
     * @param cmd The command to check.
     * @returns True if the command is local, false otherwise.
     */
    isLocalCmd(cmd: Cmd<any>) {
        return cmd.meta.origin === CmdOriginType.local;
    }

    private clearCurrentViewportHistoryIfRemoteCmd(meta: CmdMeta) {
        if (meta.origin === CmdOriginType.remote) this.editor.clearHistory(meta.viewport.id);
    }
}
