import {
    AbstractCommand,
    Cmd,
    CmdMeta,
    DocumentId,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    LayerId,
} from '@viclass/editor.core';
import { ProtoMessage, ProtoMessageStatic } from '@viclass/proto/editor.core';
import {
    CmdTypeProto,
    DocDefaultElRenderPropsProto,
    DocRenderPropProto,
    EndPreviewCmdProto,
    HighlightElementsCmdProto,
    PreviewAngleCmdProto,
    PreviewCircleShapeCmdProto,
    PreviewEllipseShapeCmdProto,
    PreviewLineCmdProto,
    PreviewPolygonCmdProto,
    PreviewSectorShapeCmdProto,
    RemoveHighlightElementsCmdProto,
    RemovePreviewElsCmdProto,
    RenderAngleCmdProto,
    RenderCircleCmdProto,
    RenderCircleShapeCmdProto,
    RenderEllipseCmdProto,
    RenderEllipseShapeCmdProto,
    RenderLineCmdProto,
    RenderPolygonCmdProto,
    RenderSectorCmdProto,
    RenderSectorShapeCmdProto,
    RenderVertexCmdProto,
    UpdateDocDefaultElRenderPropsCmdProto,
    UpdateDocStateCmdProto,
    UpdateElNameCmdProto,
    UpdateElsPropCmdProto,
} from '@viclass/proto/editor.geo';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import {
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVertex,
} from '../model';
import {
    buildPreviewAngleProto,
    buildPreviewCircleShapeProto,
    buildPreviewEllipseShapeProto,
    buildPreviewLineProto,
    buildPreviewPolygonProto,
    buildPreviewSectorShapeProto,
    buildRenderAngleProto,
    buildRenderCircleProto,
    buildRenderCircleShapeProto,
    buildRenderEllipseProto,
    buildRenderEllipseShapeProto,
    buildRenderLineProto,
    buildRenderPolygonProto,
    buildRenderSectorProto,
    buildRenderSectorShapeProto,
    buildRenderVertexProto,
} from './proto.utils';

export class FCAbstractProtoCmd<T extends ProtoMessage> extends AbstractCommand<CmdTypeProto> {
    override state: T;

    constructor(
        meta: CmdMeta,
        private stateClass: ProtoMessageStatic<T>
    ) {
        super(meta, meta.cmdType);
        this.state = new this.stateClass();
    }

    override serialize(): Uint8Array {
        return this.state.serializeBinary();
    }

    override deserialize(buf: Uint8Array): T {
        return this.stateClass.deserializeBinary(buf);
    }
}

export class UpdateDocStateCmd extends FCAbstractProtoCmd<UpdateDocStateCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, UpdateDocStateCmdProto);
    }

    setState(globalId: DocumentId, docRenderProp: DocRenderPropProto = new DocRenderPropProto()) {
        this.state.setGlobalId(globalId).setDocRenderProp(docRenderProp);
    }
}

export class UpdateDocDefaultElRenderPropsCmd extends FCAbstractProtoCmd<UpdateDocDefaultElRenderPropsCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, UpdateDocDefaultElRenderPropsCmdProto);
    }

    setState(
        globalId: DocumentId,
        docDefaultElRenderProps: DocDefaultElRenderPropsProto = new DocDefaultElRenderPropsProto()
    ) {
        this.state.setGlobalId(globalId).setDocDefaultElRenderProps(docDefaultElRenderProps);
    }
}

export class UpdateElementsPropCmd extends FCAbstractProtoCmd<UpdateElsPropCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, UpdateElsPropCmdProto);
    }
}

export class UpdateElementNameCmd extends FCAbstractProtoCmd<UpdateElNameCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, UpdateElNameCmdProto);
    }

    setRelIndex(relIndex: number): UpdateElementNameCmd {
        this.state.setRelIndex(relIndex);
        return this;
    }

    setName(name: string): UpdateElementNameCmd {
        this.state.setName(name);
        return this;
    }
}

export class HighlightElementsCmd extends FCAbstractProtoCmd<HighlightElementsCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, HighlightElementsCmdProto);
    }

    setState(layerId: LayerId, elIndexArr: number[]) {
        this.state.setRelIndexList(elIndexArr).setLayerId(layerId);
    }
}

export class RemoveHighlightElementsCmd extends FCAbstractProtoCmd<RemoveHighlightElementsCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RemoveHighlightElementsCmdProto);
    }

    setState(layerId: LayerId, elIndexArr: number[]) {
        this.state.setRelIndexList(elIndexArr).setLayerId(layerId);
    }
}

export class RemovePreviewElsCmd extends FCAbstractProtoCmd<RemovePreviewElsCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RemovePreviewElsCmdProto);
    }

    setState(elIndexArr: number[]) {
        this.state.setRelIdsList(elIndexArr);
    }
}

export class PotentialSelectionElementsCmd extends HighlightElementsCmd {}

export class RemovePotentialSelectionElementsCmd extends RemoveHighlightElementsCmd {}

export class RenderElementCmd<T extends ProtoMessage> extends FCAbstractProtoCmd<T> {
    constructor(meta: CmdMeta, stateClass: ProtoMessageStatic<T>) {
        super(meta, stateClass);
    }

    setLayer(layerId: LayerId) {
        return (this.state as any).setLayerId(layerId);
    }
}

export class EndPreviewCmd extends RenderElementCmd<EndPreviewCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, EndPreviewCmdProto);
    }
}

export class RenderVertexCmd extends RenderElementCmd<RenderVertexCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderVertexCmdProto);
    }

    setVertex(vertex: RenderVertex) {
        return this.state.setVertex(buildRenderVertexProto(vertex));
    }
}

export class RenderAngleCmd extends RenderElementCmd<RenderAngleCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderAngleCmdProto);
    }

    setAngle(angle: RenderAngle) {
        return this.state.setAngle(buildRenderAngleProto(angle));
    }
}

export class PreviewAngleCmd extends RenderElementCmd<PreviewAngleCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, PreviewAngleCmdProto);
    }

    setAngle(angle: RenderAngle) {
        return this.state.setAngle(buildPreviewAngleProto(angle));
    }
}

export class PreviewAngleByThreePointsCmd extends RenderElementCmd<PreviewAngleCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, PreviewAngleCmdProto);
    }

    setAngle(angle: RenderAngle) {
        return this.state.setAngle(buildPreviewAngleProto(angle));
    }
}

export class RenderLineCmd extends RenderElementCmd<RenderLineCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderLineCmdProto);
    }

    setLine(line: RenderLine) {
        return this.state.setLine(buildRenderLineProto(line));
    }
}

export class PreviewLineCmd extends RenderElementCmd<PreviewLineCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, PreviewLineCmdProto);
    }

    setLine(line: RenderLine) {
        return this.state.setLine(buildPreviewLineProto(line));
    }
}

export class RenderCircleShapeCmd extends RenderElementCmd<RenderCircleShapeCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderCircleShapeCmdProto);
    }

    setCircle(circle: RenderCircleShape) {
        return this.state.setCircle(buildRenderCircleShapeProto(circle));
    }
}

export class PreviewCircleShapeCmd extends RenderElementCmd<PreviewCircleShapeCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, PreviewCircleShapeCmdProto);
    }

    setCircle(circle: RenderCircleShape) {
        return this.state.setCircle(buildPreviewCircleShapeProto(circle));
    }
}

export class RenderSectorShapeCmd extends RenderElementCmd<RenderSectorShapeCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderSectorShapeCmdProto);
    }

    setSector(circularSector: RenderSectorShape) {
        return this.state.setSector(buildRenderSectorShapeProto(circularSector));
    }
}

export class PreviewSectorShapeCmd extends RenderElementCmd<PreviewSectorShapeCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, PreviewSectorShapeCmdProto);
    }

    setSector(circularSector: RenderSectorShape) {
        return this.state.setSector(buildPreviewSectorShapeProto(circularSector));
    }
}

export class RenderSectorCmd extends RenderElementCmd<RenderSectorCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderSectorCmdProto);
    }

    setArc(arc: RenderSector) {
        return this.state.setArc(buildRenderSectorProto(arc));
    }
}

export class RenderEllipseCmd extends RenderElementCmd<RenderEllipseCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderEllipseCmdProto);
    }

    setArc(arc: RenderEllipse) {
        return this.state.setArc(buildRenderEllipseProto(arc));
    }
}

export class RenderCircleCmd extends RenderElementCmd<RenderCircleCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderCircleCmdProto);
    }

    setArc(arc: RenderCircle) {
        return this.state.setArc(buildRenderCircleProto(arc));
    }
}

export class RenderPolygonCmd extends RenderElementCmd<RenderPolygonCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderPolygonCmdProto);
    }

    setPolygon(polygon: RenderPolygon) {
        return this.state.setPolygon(buildRenderPolygonProto(polygon));
    }
}

export class RenderEllipseShapeCmd extends RenderElementCmd<RenderEllipseShapeCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, RenderEllipseShapeCmdProto);
    }

    setEllipse(ellipse: RenderEllipseShape) {
        return this.state.setEllipse(buildRenderEllipseShapeProto(ellipse));
    }
}

export class PreviewPolygonCmd extends RenderElementCmd<PreviewPolygonCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, PreviewPolygonCmdProto);
    }

    setPolygon(polygon: RenderPolygon) {
        return this.state.setPolygon(buildPreviewPolygonProto(polygon));
    }
}

export class PreviewEllipseShapeCmd extends RenderElementCmd<PreviewEllipseShapeCmdProto> {
    constructor(meta: CmdMeta) {
        super(meta, PreviewEllipseShapeCmdProto);
    }

    setEllipse(ellipse: RenderEllipseShape) {
        return this.state.setEllipse(buildPreviewEllipseShapeProto(ellipse));
    }
}

export function deserializer(meta: CmdMeta, stateData: Uint8Array): Cmd<FCCmdTypeProto | CmdTypeProto> {
    let cmd: Cmd<CmdTypeProto | FCCmdTypeProto>;
    const cmdType = meta.cmdType as CmdTypeProto | FCCmdTypeProto;

    switch (cmdType) {
        case FCCmdTypeProto.INSERT_DOC: {
            cmd = new FCInsertDocCmd(meta);
            break;
        }

        case FCCmdTypeProto.INSERT_LAYER: {
            cmd = new FCInsertLayerCmd(meta);
            break;
        }

        case FCCmdTypeProto.PREVIEW_BOUNDARY: {
            cmd = new FCPreviewBoundaryCmd(meta);
            break;
        }

        case FCCmdTypeProto.REMOVE_DOC: {
            cmd = new FCRemoveDocCmd(meta);
            break;
        }

        case FCCmdTypeProto.RELOAD_DOC: {
            cmd = new FCReloadDocCmd(meta);
            break;
        }

        case FCCmdTypeProto.UPDATE_BOUNDARY: {
            cmd = new FCUpdateDocCmd(meta);
            break;
        }

        case CmdTypeProto.UPDATE_DOC_STATE: {
            cmd = new UpdateDocStateCmd(meta);
            break;
        }

        case CmdTypeProto.UPDATE_DEFAULT_EL_RENDER_PROPS: {
            cmd = new UpdateDocDefaultElRenderPropsCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_VERTEX: {
            cmd = new RenderVertexCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_LINE: {
            cmd = new RenderLineCmd(meta);
            break;
        }
        case CmdTypeProto.PREVIEW_LINE: {
            cmd = new PreviewLineCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_CIRCLE_SHAPE: {
            cmd = new RenderCircleShapeCmd(meta);
            break;
        }
        case CmdTypeProto.PREVIEW_CIRCLE_SHAPE: {
            cmd = new PreviewCircleShapeCmd(meta);
            break;
        }

        case CmdTypeProto.REMOVE_PREVIEW_BY_REL_IDS: {
            cmd = new RemovePreviewElsCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_CIRCULAR_SECTOR_SHAPE: {
            cmd = new RenderSectorShapeCmd(meta);
            break;
        }
        case CmdTypeProto.PREVIEW_CIRCULAR_SECTOR_SHAPE: {
            cmd = new PreviewSectorShapeCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_SECTOR: {
            cmd = new RenderSectorCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_ELLIPSE: {
            cmd = new RenderEllipseCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_CIRCLE: {
            cmd = new RenderCircleCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_POLYGON: {
            cmd = new RenderPolygonCmd(meta);
            break;
        }
        case CmdTypeProto.PREVIEW_POLYGON: {
            cmd = new PreviewPolygonCmd(meta);
            break;
        }

        case CmdTypeProto.RENDER_ANGLE: {
            cmd = new RenderAngleCmd(meta);
            break;
        }
        case CmdTypeProto.PREVIEW_ANGLE: {
            cmd = new PreviewAngleCmd(meta);
            break;
        }
        case CmdTypeProto.PREVIEW_ELLIPSE_SHAPE: {
            cmd = new PreviewEllipseShapeCmd(meta);
            break;
        }
        case CmdTypeProto.RENDER_ELLIPSE_SHAPE: {
            cmd = new RenderEllipseShapeCmd(meta);
            break;
        }
        case CmdTypeProto.HIGHLIGHT_ELEMENTS: {
            cmd = new HighlightElementsCmd(meta);
            break;
        }
        case CmdTypeProto.REMOVE_HIGHLIGHT_ELEMENTS: {
            cmd = new RemoveHighlightElementsCmd(meta);
            break;
        }
        case CmdTypeProto.UPDATE_EL_NAME: {
            cmd = new UpdateElementNameCmd(meta);
            break;
        }
        case CmdTypeProto.UPDATE_ELS_PROP: {
            cmd = new UpdateElementsPropCmd(meta);
            break;
        }
        case CmdTypeProto.END_PREVIEW: {
            cmd = new EndPreviewCmd(meta);
            break;
        }

        default:
            throw new Error(`invalid cmd type: ${cmdType}`);
    }

    cmd.state = cmd.deserialize(stateData);

    return cmd;
}
