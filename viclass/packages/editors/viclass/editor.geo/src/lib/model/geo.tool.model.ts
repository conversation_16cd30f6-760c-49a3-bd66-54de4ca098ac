import {
    DocFocusedES,
    DocLocalId,
    FocusDocEvent,
    Position,
    ProxyObject,
    ProxyObjectConfig,
    ToolState,
} from '@viclass/editor.core';
import { GeoDocCtrl } from '../objects';
import { ConstraintTemplateData, DocRenderProp } from './gateway.models';
import { GeoObjectType, UserConstraintInputData } from './geo.models';
import { DefaultGeoRenderProp, GeoRelType, GeoRenderElement, SettingPropertyType } from './render.elements.model';

/**
 *
 * <AUTHOR>
 */

export class CommonToolState implements ToolState {}

export class GeoDocFloatingUIToolState implements ToolState {
    position: Position;
    show: boolean = true;
}

export class GeoPanToolState implements ToolState {
    doc: GeoDocCtrl;
}

export class GeoZoomToolState implements ToolState {
    zoomLevel: number;
}

export class RegularPolygonToolState extends CommonToolState {
    noEdge: number = 3;
    selecting: number = 1; // the current vertex being selected
    nth: number = 1;
}

export class TriangleToolState extends CommonToolState {
    createFromBase: boolean = true;
}

export class QuadToolState extends CommonToolState {
    sideOrDiagonal: 'side' | 'diagonal' = 'side';
}

export interface ElementItem {
    relIdx: number;
    name: string;
    elType: GeoObjectType;
    relType: GeoRelType;
    hidden: boolean;
    highlighted: boolean;
    selected: boolean;
    depIdxes?: number[];
}

export type ElementItemActionType =
    | 'highlight'
    | 'remove-highlight'
    | 'select'
    | 'select-multiple'
    | 'hide'
    | 'show'
    | 'delete'
    | 'setting'
    | 'deps';

export interface ElementItemAction {
    actionType: ElementItemActionType;
    relIdx: number;
}

export class ListElementToolState implements ProxyObjectConfig<ListElementToolState> {
    _proxied: boolean = true;
    private _listenProps = ['action', 'elements', 'editMode'];

    elements: ElementItem[];
    editMode: boolean = false;

    action?: ElementItemAction;

    listenProps(prop: string): boolean {
        return this._listenProps.includes(prop);
    }
}

export class UpdatePropToolState implements ToolState, ProxyObjectConfig<UpdatePropToolState> {
    _proxyObj?: ProxyObject<UpdatePropToolState>;
    _proxied: boolean = true;
    private _listenProps = [
        'notProcess',
        'doc',
        'elementProps',
        'docRenderProp',
        'selectedElements',
        'isSaveToHistory',
        'docDefaultElRenderProps',
    ];

    notProcess: boolean; // don't process to update state, when tool ui receive event, only refresh ui to show prop state
    doc: GeoDocCtrl;
    elementProps: {
        [key in SettingPropertyType]: any;
    } = {} as any;
    docRenderProp: DocRenderProp = {} as DocRenderProp;
    docDefaultElRenderProps: DefaultGeoRenderProp = undefined;
    selectedElements: GeoRenderElement[] = [];
    isSaveToHistory?: boolean; // If true or undefined, save to history. If false, do not save.
    sectors: GeoRenderElement[] = [];

    reset() {
        this.doc = undefined;
        this.notProcess = true;
        this.elementProps = {} as any;
        this.docRenderProp = {} as DocRenderProp;
        this.selectedElements = [];
        this.sectors = [];
        this.isSaveToHistory = undefined;
    }

    listenProps(prop: string): boolean {
        return this._listenProps.includes(prop);
    }
}

export interface ValidationResult {
    valid: boolean;
    message?: string;
}

export declare type NamingUserAction = 'confirm' | 'auto' | 'cancel';

export interface UserInputResult {
    action: NamingUserAction;
    data?: any;
}

export class NamingElementToolState implements ToolState, ProxyObjectConfig<NamingElementToolState> {
    private _listenProps = ['notProcess', 'typeValidating', 'idxValidating', 'validateResult', 'result', 'requireName'];

    requireName: boolean;
    result: UserInputResult;
    requireNameForUsers: RequireNameForUser[];
    idxValidating?: number;
    typeValidating?: GeoRelType;
    validateResult?: ValidationResult[];

    // don't process to update state, when tool ui receive event, only refresh ui to show state
    notProcess?: boolean;

    _proxied: boolean = true;
    _proxyObj: ProxyObject<NamingElementToolState>;

    reset() {
        this.notProcess = true;
        this.requireName = false;
        this.result = undefined;
        this.validateResult = [];
        this.requireNameForUsers = [];
    }

    listenProps(prop: string): boolean {
        return this._listenProps.includes(prop);
    }
}

export class RenameElementToolState implements ToolState, ProxyObjectConfig<RenameElementToolState> {
    _proxied: boolean = true;
    private _listenProps = ['notProcess', 'name', 'validateResult'];

    docLocalId: DocLocalId;
    relIndex: number;

    // don't process to update state, when tool ui receive event, only refresh ui to show prop state
    notProcess?: boolean;

    validateResult?: { valid: boolean; message: string };

    name?: string;

    reset() {
        this.docLocalId = undefined;
        this.notProcess = true;
        this.name = undefined;
    }

    listenProps(prop: string): boolean {
        return this._listenProps.includes(prop);
    }
}

export interface InputCommandToolState extends ToolState {
    objTypeStr: Map<GeoObjectType, string>;
    objType?: GeoObjectType;
    elName?: string;
    tplKeyword?: string;
    templates: ConstraintTemplateData[];
    userConstraintInput?: UserConstraintInputData;
}

export class InputCommandToolStateImpl implements InputCommandToolState, ProxyObjectConfig<InputCommandToolStateImpl> {
    _proxied: boolean = true;
    private readonly _listenProps = ['objType', 'elName', 'tplKeyword', 'templates', 'userConstraintInput'];

    objType?: GeoObjectType;
    elName?: string;
    tplKeyword?: string;

    templates: ConstraintTemplateData[] = [];
    objTypeStr: Map<GeoObjectType, string> = new Map();

    userConstraintInput: UserConstraintInputData;

    constructor(objTypeStr: Map<GeoObjectType, string>) {
        this.objTypeStr = objTypeStr;
    }

    listenProps(prop: string): boolean {
        return this._listenProps.includes(prop);
    }
}

export type GeometryDocFocusedES = DocFocusedES<GeoDocCtrl>;

export type GeometryDocEvent = FocusDocEvent<GeoDocCtrl>;

export type RequestElementName = {
    objName: string;
    originElement: GeoRenderElement[];
    pickName: (ctrl: GeoDocCtrl, nameExcluded?: string[]) => string;
    namesToAvoid: string[];
    onValidate?: (idx: number, name: string, type: GeoRelType) => ValidationResult;
};

export type RequireNameForUser = {
    type: GeoRelType;
    objType: string;
    originElNames: string[];
    inputElNames: string[];
};
