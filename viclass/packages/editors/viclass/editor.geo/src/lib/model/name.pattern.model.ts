import { GeoRelType } from './render.elements.model';

export declare type NamePatternItem = {
    relType: GeoRelType[];
    name: string;
    regex: RegExp[];
};

export const RenderNamePatternModel = {
    RenderVertex: {
        relType: ['Vertex'],
        name: '<PERSON><PERSON>ể<PERSON>',
        regex: [/^[A-Z]\d*'?$/],
    },
    RenderLine: {
        relType: ['Line', 'LineSegment', 'Vector', 'Ray'],
        name: 'Đường thẳng',
        regex: [/^[a-z]\d*'?$/],
    },
    RenderLineSegment: {
        relType: ['Line', 'LineSegment', 'Vector', '<PERSON>'],
        name: '<PERSON><PERSON><PERSON><PERSON> thẳng',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    RenderVector: {
        relType: ['Line', 'LineSegment', 'Vector', '<PERSON>'],
        name: 'Vector',
        regex: [/^[a-z]\d*'?$/],
    },
    RenderRay: {
        relType: ['Line', 'LineSegment', 'Vector', '<PERSON>'],
        name: '<PERSON><PERSON>',
        regex: [/^([A-Z]\d*'?)([A-Za-z]\d*'?)$/],
    },
    RenderPolygon: {
        relType: ['Polygon'],
        name: 'Đa giác',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)*$/],
    },
    RenderTriangle: {
        relType: ['Polygon'],
        name: 'Tam giác',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    RenderQuadrilateral: {
        relType: ['Polygon'],
        name: 'Tứ giác',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    RenderCircleShape: {
        relType: ['Circle'],
        name: 'hình tròn',
        regex: [/^[a-z]\d*'?$/],
    },
    RenderEllipseShape: {
        relType: ['Ellipse'],
        name: 'Hình elip',
        regex: [/^[a-z]\d*'?$/],
    },
    RenderSectorShape: {
        relType: ['CircularSector'],
        name: 'Hình quạt',
        regex: [/^[a-z]\d*'?$/],
    },
    RenderSemicircle: {
        relType: ['CircularSector'],
        name: 'Hình bán nguyệt',
        regex: [/^([A-Z]\d*'?)([A-Z]\d*'?)$/],
    },
    RenderAngle: {
        relType: ['Angle'],
        name: 'Góc',
        regex: [/^([A-Z]\d*'?)$/, /^([A-Za-z]\d*'?)([A-Z]\d*'?)([A-Za-z]\d*'?)$/],
    },
};
