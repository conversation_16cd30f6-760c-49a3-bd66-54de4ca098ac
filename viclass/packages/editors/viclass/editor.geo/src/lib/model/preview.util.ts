import { point, segment, line, vector } from '@flatten-js/core';
import {
    _angleGeoRenderProp,
    GeoRenderElement,
    NOT_SET_VALUE,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderVertex,
} from '.';
import { syncPreviewCommands } from '../cmd';
import { GeoDocCtrl } from '../objects';
import { GeoRenderer } from '../renderer';
import { buildPreviewAngleRenderProp } from '../tools/tool.utils';

function validatePreviewId(id: number) {
    if (id > 0) throw new Error('Preview Id should be smaller than zero');
}

function idxOk(id?: number) {
    return id !== undefined && id !== NOT_SET_VALUE;
}

// Utility functions for create preview
export function pVertex(id: number, pos: number[]): RenderVertex {
    validatePreviewId(id);

    const el: RenderVertex = Object.assign(new RenderVertex(), {
        relIndex: id,
        coords: pos,
        name: undefined,
        usable: true,
        valid: true,
    });

    return el;
}

function addRefPEl(el: GeoRenderElement, refPEl: GeoRenderElement[]): boolean {
    if (el.relIndex < 0) {
        refPEl.push(el);
        return true;
    }

    return false;
}

/**
 * Create a line with preview information from start / end points or their coordinates
 * If a vector is supplied, then end point should be undefined
 * @param ctrl
 * @param id
 * @param clsK
 * @param startPoint
 * @param endPoint
 * @returns
 */
export function pLine(
    ctrl: GeoDocCtrl,
    id: number,
    clsK: new (...args) => RenderLine,
    startPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[] | undefined,
    vector: number[] | undefined = undefined
): RenderLine {
    const linePartial: Partial<RenderLine> = {
        relIndex: id,
        name: '',
        startPointIdx: startPoint instanceof RenderVertex ? startPoint.relIndex : NOT_SET_VALUE,
        endPointIdx: endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
            sVCoords: undefined,
            eVCoords: undefined,
        },
    };
    const el: RenderLine = Object.assign(new clsK(), linePartial);

    // if the point is added to refPEL, then we don't need to set the coord, else possibly
    let needSCoord = startPoint instanceof RenderVertex ? !addRefPEl(startPoint, el.pInfo.refPEl) : true;
    let needECoord = endPoint instanceof RenderVertex ? !addRefPEl(endPoint, el.pInfo.refPEl) : true;

    // if also exist in list of existing elements, don't need to include coords separately
    if (needSCoord && idxOk(el.startPointIdx) && ctrl.rendererCtrl.elementAt(el.startPointIdx)) needSCoord = false;
    if (needECoord && idxOk(el.endPointIdx) && ctrl.rendererCtrl.elementAt(el.endPointIdx)) needECoord = false;

    // still need the coord?
    if (needSCoord) el.pInfo.sVCoords = startPoint instanceof RenderVertex ? startPoint.coords : startPoint;
    if (needECoord) el.pInfo.eVCoords = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;

    // set the vector from the points
    const sCoord = el.coord('start', ctrl.rendererCtrl);
    const eCoord = el.coord('end', ctrl.rendererCtrl);

    el.vector = vector ? vector : [eCoord[0] - sCoord[0], eCoord[1] - sCoord[1], 0];

    return el;
}

/**
 * Internal helper that builds any “circle-like” element (RenderCircle | RenderCircleShape)
 * – The only thing that can vary is the concrete constructor and extra fields (e.g. `arcRelIdx`).
 */
function buildCircleLike<T extends RenderCircle | RenderCircleShape>(
    ctor: new () => T,
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    extra: Partial<T> = {} // allow caller to inject specialised props
): T {
    const relIdxs = [
        centerPoint instanceof RenderVertex ? centerPoint.relIndex : NOT_SET_VALUE,
        endPoint instanceof RenderVertex ? endPoint.relIndex : NOT_SET_VALUE,
    ];

    const base: Partial<T> = {
        relIndex: id,
        name: '',
        centerPointIdx: relIdxs[0],
        vertexRelIdxes: relIdxs,
        usable: true,
        valid: true,
        pInfo: { refPEl: [], cCoords: undefined },
        ...extra,
    };

    const el = Object.assign(new ctor(), base);

    // reference-point bookkeeping
    let needCoords = centerPoint instanceof RenderVertex ? !addRefPEl(centerPoint, el.pInfo.refPEl) : true;

    if (needCoords && idxOk(el.centerPointIdx) && ctrl.rendererCtrl.elementAt(el.centerPointIdx)) {
        needCoords = false;
    }

    if (needCoords) {
        el.pInfo.cCoords = centerPoint instanceof RenderVertex ? centerPoint.coords : centerPoint;
    }

    // radius from centre → end
    const c = el.coord('center', ctrl.rendererCtrl);
    const e = endPoint instanceof RenderVertex ? endPoint.coords : endPoint;
    el.radius = point(c[0], c[1]).distanceTo(point(e[0], e[1]))[0];

    return el;
}

/** Original `pCircle` now just delegates to the helper. */
export function pCircle(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[]
): RenderCircle {
    return buildCircleLike(RenderCircle, ctrl, id, centerPoint, endPoint);
}

/** Original `pCircleShape` with the only extra bit: `arcRelIdx`. */
export function pCircleShape(
    ctrl: GeoDocCtrl,
    id: number,
    centerPoint: RenderVertex | number[],
    endPoint: RenderVertex | number[],
    arc: RenderCircle | number
): RenderCircleShape {
    const el = buildCircleLike(RenderCircleShape, ctrl, id, centerPoint, endPoint, {
        arcRelIdx: arc instanceof RenderCircle ? arc.relIndex : arc,
    } as Partial<RenderCircleShape>);

    if (arc instanceof RenderCircle) addRefPEl(arc, el.pInfo.refPEl);

    return el;
}

/**
 *
 * @param ctrl
 * @param id
 * @param verts
 * @param withBoundary
 * @returns
 */
export function pPolygon(
    ctrl: GeoDocCtrl,
    id: number,
    verts: (RenderVertex | number[])[],
    withBoundary: boolean,
    lineType?: new (...args: any[]) => RenderLine
) {
    const polygon = new RenderPolygon();
    const polygonPartial: Partial<RenderPolygon> = {
        relIndex: id,
        name: '',
        usable: true,
        valid: true,
        faces: verts.map(v => (v instanceof RenderVertex ? v.relIndex : NOT_SET_VALUE)),
        pInfo: {
            refPEl: [],
            verts: new Map(),
        },
    };

    verts.forEach((v, i) => {
        // for each vert, we try to use it
        if (v instanceof RenderVertex) {
            const addedRef = addRefPEl(v, polygonPartial.pInfo.refPEl);

            if (!addedRef) polygonPartial.pInfo.verts.set(i, [...v.coords]);
        } else {
            polygonPartial.pInfo.verts.set(i, [...v]);
        }

        if (withBoundary && i < verts.length) {
            const j = (i + 1) % verts.length;
            const edge = pLine(
                ctrl,
                id - j - 5,
                lineType ? lineType : RenderLineSegment,
                verts[i],
                verts[j],
                undefined
            );
            addRefPEl(edge, polygonPartial.pInfo.refPEl);
        }
    });

    Object.assign(polygon, polygonPartial);

    return polygon;
}

/**
 * Create an angle with preview information from two intersecting lines
 * @param ctrl
 * @param id
 * @param line1 - The first line forming the angle
 * @param line2 - The second line forming the angle
 * @returns
 */
export function pAngle(ctrl: GeoDocCtrl, id: number, line1: RenderLine, line2: RenderLine): RenderAngle {
    validatePreviewId(id);

    // Get line coordinates using flatten-js
    const line1Start = line1.coord('start', ctrl.rendererCtrl);
    const line1End = line1.coord('end', ctrl.rendererCtrl);
    const line2Start = line2.coord('start', ctrl.rendererCtrl);
    const line2End = line2.coord('end', ctrl.rendererCtrl);

    // Create flatten-js geometric objects
    const flattenLine1 = line(point(line1Start[0], line1Start[1]), point(line1End[0], line1End[1]));
    const flattenLine2 = line(point(line2Start[0], line2Start[1]), point(line2End[0], line2End[1]));

    // Calculate intersection point using flatten-js
    const intersections = flattenLine1.intersect(flattenLine2);
    let vertexCoords: number[];

    if (intersections.length > 0) {
        // Use the first intersection point
        const intersectionPt = intersections[0];
        vertexCoords = [intersectionPt.x, intersectionPt.y];
    } else {
        // Lines are parallel or don't intersect, use midpoint of line1 as vertex
        const seg1 = segment(point(line1Start[0], line1Start[1]), point(line1End[0], line1End[1]));
        const midpoint = seg1.middle();
        vertexCoords = [midpoint.x, midpoint.y];
    }

    // Create direction vectors using flatten-js
    const line1Vec = vector(point(line1Start[0], line1Start[1]), point(line1End[0], line1End[1]));
    const line2Vec = vector(point(line2Start[0], line2Start[1]), point(line2End[0], line2End[1]));

    const anglePartial: Partial<RenderAngle> = {
        relIndex: id,
        name: '',
        anglePointIdx: NOT_SET_VALUE, // No specific vertex point index since it's calculated
        usable: true,
        valid: true,
        pInfo: {
            refPEl: [],
            cCoords: vertexCoords,
        },
    };

    const el: RenderAngle = Object.assign(new RenderAngle(buildPreviewAngleRenderProp()), anglePartial);

    // Add the lines as reference elements
    addRefPEl(line1, el.pInfo.refPEl);
    addRefPEl(line2, el.pInfo.refPEl);

    // Use the direction vectors from flatten-js
    el.vectorStart = [line1Vec.x, line1Vec.y, 0];
    el.vectorEnd = [line2Vec.x, line2Vec.y, 0];

    // Ensure no duplicate reference elements
    el.pInfo.refPEl = Array.from(new Set(el.pInfo.refPEl));

    return el;
}

/**
 * Get reference element from index. This will first check the the renderer
 * if the element is there, then use it, if not it check the reference elements
 * included as part of the preview.
 * @param idx
 */
export function refElFromIdx(idx: number, host: GeoRenderElement, renderer: GeoRenderer): GeoRenderElement | undefined {
    const el = renderer.elementAt(idx);

    if (el) return el;

    const pel = renderer.previewElAt(idx);
    if (pel) return pel;

    const refEl = host.pInfo?.refPEl.find(re => re.relIndex == idx);
    if (refEl) return refEl;

    return undefined;
}

/**
 * Utility class to ensure only necessary preview is synchronized
 */
export class PreviewQueue {
    private queue: GeoRenderElement[] = [];

    add(element: GeoRenderElement): void {
        if (element) {
            // Ensure no duplicates by relIndex
            if (!this.queue.find(el => el.relIndex === element.relIndex)) {
                this.queue.push(element);
            }
        }
    }

    async flush(docCtrl: GeoDocCtrl): Promise<void> {
        if (!docCtrl || this.queue.length === 0) {
            this.queue = []; // Clear queue if no docCtrl or empty
            return;
        }

        const elementsToSync = new Map<number, GeoRenderElement>();
        this.queue.forEach(el => elementsToSync.set(el.relIndex, el));

        this.queue.forEach(el => {
            if (el.pInfo && el.pInfo.refPEl) {
                el.pInfo.refPEl.forEach(refEl => {
                    elementsToSync.delete(refEl.relIndex);
                });
            }
        });

        for (const el of Array.from(elementsToSync.values())) {
            await syncPreviewCommands(el, docCtrl);
        }
        this.queue = [];
    }
}

/**
 * setAnglePInfo: Utility to create a RenderAngle with preview info, for use in preview commands.
 * @param el The RenderAngle element (as target)
 * @param rendererCtrl The renderer controller (GeoRenderer)
 * @returns The updated RenderAngle with preview info
 */
export function setAnglePInfo(el: RenderAngle, rendererCtrl: GeoRenderer): RenderAngle {
    if (el.pInfo && el.pInfo.refPEl && el.pInfo.refPEl.length > 0) return el;

    // Since pAngle now requires RenderLine objects, we need to create temporary lines
    // from the vector information stored in the angle
    const vertexIdx = el.anglePointIdx;
    const vertex = rendererCtrl.elementAt(vertexIdx) || rendererCtrl.previewElAt(vertexIdx);
    const vertexCoords = (vertex as RenderVertex)?.coords || el.pInfo?.cCoords || [0, 0];
    const startVector = el.vectorStart || [1, 0, 0];
    const endVector = el.vectorEnd || [0, 1, 0];

    // Create temporary lines from the vectors
    const startLineEnd = [vertexCoords[0] + startVector[0], vertexCoords[1] + startVector[1]];
    const endLineEnd = [vertexCoords[0] + endVector[0], vertexCoords[1] + endVector[1]];

    const line1 = pLine(rendererCtrl.docCtrl, el.relIndex - 1000, RenderLine, vertexCoords, startLineEnd);
    const line2 = pLine(rendererCtrl.docCtrl, el.relIndex - 2000, RenderLine, vertexCoords, endLineEnd);

    const preview = pAngle(rendererCtrl.docCtrl, el.relIndex, line1, line2);
    el.pInfo = preview.pInfo;
    el.vectorStart = preview.vectorStart;
    el.vectorEnd = preview.vectorEnd;
    return el;
}
