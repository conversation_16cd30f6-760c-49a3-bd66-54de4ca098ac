import { GeoRenderElement } from '../model';
import { GeoPointerEvent } from '../model/geo.models';
import { CommonToolState } from '../model/geo.tool.model';
import { GeoSelectHitContext } from '../objects';
import { GeometryTool } from '../tools/geo.tool';
import { getPointAndVertex } from '../tools/tool.utils';

export class PotentialSelectionDelegator<T extends GeometryTool<CommonToolState>> {
    private lastPotentialHitCtx: GeoSelectHitContext = undefined;
    private cacheFilterElementFunc: (el: GeoRenderElement) => boolean = undefined;

    constructor(private geoTool: T) {}

    public checkPotentialAreaAndClearIfOut(event: GeoPointerEvent): boolean {
        if (this.lastPotentialHitCtx) {
            if (this.geoTool.isPreselectionHit(event)) {
                return false;
            }

            this.deactivate();
            return true;
        }

        const { hitCtx, hitEl } = getPointAndVertex(this.geoTool, event);
        if (hitCtx && hitCtx.hitDetails?.el && hitEl.type == 'RenderVertex') this.activate(hitCtx);
        return true;
    }

    public clearPotential() {
        this.deactivate();
    }

    private deactivate() {
        if (!this.lastPotentialHitCtx) return;
        this.geoTool.editor.removePotentialSelection(this.lastPotentialHitCtx);
        this.geoTool.editor.filterElementFunc = this.cacheFilterElementFunc;
        this.cacheFilterElementFunc = undefined;
        this.lastPotentialHitCtx = undefined;
    }

    private activate(hitCtx: GeoSelectHitContext) {
        this.lastPotentialHitCtx = hitCtx;
        this.geoTool.editor.potentialSelection(this.lastPotentialHitCtx);
        this.cacheFilterElementFunc = this.geoTool.editor.filterElementFunc;
        this.geoTool.editor.filterElementFunc = (el: GeoRenderElement) => el === this.lastPotentialHitCtx.hitDetails.el;
    }
}
