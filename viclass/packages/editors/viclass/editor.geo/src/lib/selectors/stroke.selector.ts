// --------------- STROKE SELECTOR -------------------------------------------------------------------

import { <PERSON><PERSON>r<PERSON><PERSON>lerDecorator, LocatableEvent } from '@viclass/editor.core';
import { syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeoRelType, GeoRenderElement, RenderVertex, StrokeType } from '../model';
import { pVertex } from '../model/preview.util';
import { GeoDocCtrl, GeoSelectHitContext } from '../objects';
import { posInGeo, projectOnEl } from '../tools/tool.utils';
import { ElementSelector, PreviewIds, SelectableType, SelectorOptions } from './selectors';

export type StrokeSelectorOptions<T extends SelectableType> = Omit<
    SelectorOptions<T>,
    'stickyOnMatch' | 'stickyPTypes'
> & {
    selectableStrokeTypes?: GeoRelType[];
    cfunc?: (el: StrokeType) => boolean;
};

export type VertexOnStrokeSelectorOptions<T extends SelectableType> = StrokeSelectorOptions<T> & {
    tfunc?: (el: StrokeType, pEl: RenderVertex, doc: GeoDocCtrl) => RenderVertex;
    showPosHint?: boolean; // whether to show the ratio of the vertex on the shape, or the angle it makes (circle, shape, ellipse)
};

const defaultSelectableStrokeTypes: GeoRelType[] = [
    'RenderLine',
    'RenderLineSegment',
    'RenderVector',
    'RenderRay',
    'RenderSector',
    'RenderEllipse',
    'RenderCircle',
];

abstract class BaseStrokeSelector<T extends SelectableType> extends ElementSelector<T, SelectorOptions<T>> {
    protected lastHitEl?: StrokeType;
    protected curDoc?: GeoDocCtrl;

    multiple: boolean = false;

    isAccepted = false;

    declare options?: StrokeSelectorOptions<T>;

    constructor(options?: Partial<StrokeSelectorOptions<T>>) {
        super();

        this.initOptions(options);

        this.resetId();
    }

    protected initOptions(options?: Partial<StrokeSelectorOptions<T>>) {
        const defaultOpt: StrokeSelectorOptions<T> = this.defaultOptions();

        if (options) Object.assign(defaultOpt, options);

        this.options = defaultOpt;
    }

    protected defaultOptions(): StrokeSelectorOptions<T> {
        return {
            highlightOnMatch: true,
            renderEl: true,
            preview: false,
            autoAcceptOnPointerUp: true,
            selectableStrokeTypes: [...defaultSelectableStrokeTypes],
        };
    }

    private filterElementFunc = (el: GeoRenderElement): boolean => {
        const selectableTypes = this.options?.selectableStrokeTypes || defaultSelectableStrokeTypes;
        if (!selectableTypes.includes(el.type as GeoRelType)) {
            return false;
        }

        if (this.options?.refinedFilter && !this.options.refinedFilter(el)) {
            return false;
        }
        return true;
    };

    override reset(keepPreview: boolean = false): void {
        this.clearTrial(this.curDoc);

        super.reset(keepPreview);
        this.lastHitEl = undefined;
        this.curDoc = undefined;
        this.isAccepted = false;
        this.selected = undefined;
    }

    protected getHitContext(doc: GeoDocCtrl, event: LocatableEvent<any>): GeoSelectHitContext | undefined {
        const cachedFilter = doc.editor.filterElementFunc;
        let hitCtx: GeoSelectHitContext | undefined;

        try {
            doc.editor.filterElementFunc = this.filterElementFunc;
            let hitCtxOriginal: GeoSelectHitContext | undefined;
            if (this.options?.renderEl === undefined || this.options?.renderEl === true) {
                hitCtxOriginal = doc.editor.checkHitInternal(doc.layers[0], event, false, false);
            }

            // check if the hit el is VALID by calling cFunc if any
            if (hitCtxOriginal?.hitDetails?.el && this.options?.cfunc) {
                if (!this.options.cfunc(hitCtxOriginal.hitDetails.el as StrokeType)) {
                    hitCtxOriginal = undefined; // if the hit el is not valid, we just ignore it
                }
            }

            let hitCtxPreview: GeoSelectHitContext | undefined;
            if (this.options?.preview && (!hitCtxOriginal || !hitCtxOriginal.hitDetails)) {
                hitCtxPreview = doc.editor.checkHitInternal(doc.layers[0], event, false, true);
            }

            // check if the hit el is VALID by calling cFunc if any
            if (hitCtxPreview?.hitDetails?.el && this.options?.cfunc) {
                if (!this.options.cfunc(hitCtxPreview.hitDetails.el as StrokeType)) {
                    hitCtxPreview = undefined; // if the hit el is not valid, we just ignore it
                }
            }

            hitCtx = hitCtxOriginal?.hitDetails ? hitCtxOriginal : hitCtxPreview;
        } finally {
            doc.editor.filterElementFunc = cachedFilter;
        }
        return hitCtx;
    }

    // we won't generate complex shape preview automatically
    override tryPreview() {
        return undefined;
    }

    override clearTrial(doc: GeoDocCtrl) {
        if (this.lastHitEl && this.curDoc && this.options?.highlightOnMatch) {
            this.removeHighlight([this.lastHitEl], doc);
        }
    }
}

export class StrokeSelector extends BaseStrokeSelector<StrokeType> {
    /**
     * This one doesn't generate any preview
     */
    override resetId(): void {
        this.curGeneratedId = -1;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    override tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): StrokeType | undefined {
        if (this.acceptMarked) return undefined;

        this.curDoc = doc;

        const hitCtx = this.getHitContext(doc, event);
        let hitEl = hitCtx?.hitDetails?.el as StrokeType | undefined;

        if (hitEl && this.options?.cfunc && !this.options.cfunc(hitEl)) {
            hitEl = undefined;
        }

        if (this.lastHitEl && this.lastHitEl !== hitEl && this.options?.highlightOnMatch) {
            this.removeHighlight([this.lastHitEl], doc);
        }

        if (hitEl && this.options?.highlightOnMatch && this.lastHitEl !== hitEl) {
            this.highlight([hitEl], doc);
        }

        this.lastHitEl = hitEl;

        if (this.options?.autoAcceptOnPointerUp && event.eventType === 'pointerup' && hitEl) {
            this.accept(hitEl, doc);
        }

        return hitEl;
    }
}

export function stroke(options?: Partial<StrokeSelectorOptions<StrokeType>>): StrokeSelector {
    return new StrokeSelector(options);
}

export function vertexOnStroke(
    options?: Partial<VertexOnStrokeSelectorOptions<VertexOnStroke>>
): VertexOnStrokeSelector {
    return new VertexOnStrokeSelector(options);
}

/**
 * This selector select a stroke, and if a hit is match, it calculates
 * a preview point on the stroke element and return both the preview point and the
 */
export type VertexOnStroke = [StrokeType, RenderVertex];

export class VertexOnStrokeSelector extends BaseStrokeSelector<VertexOnStroke> {
    curPreviewEl?: RenderVertex;
    declare options?: VertexOnStrokeSelectorOptions<VertexOnStroke>;

    constructor(options?: Partial<VertexOnStrokeSelectorOptions<VertexOnStroke>>) {
        super(options);
    }

    protected override resetId() {
        this.curGeneratedId = PreviewIds.vertex--;
    }

    protected override defaultOptions(): VertexOnStrokeSelectorOptions<VertexOnStroke> {
        const defaultOpt: VertexOnStrokeSelectorOptions<VertexOnStroke> = {
            ...super.defaultOptions(),
            syncPreview: true,
        };

        return defaultOpt;
    }

    override reset(keepPreview: boolean = false) {
        if (!keepPreview && this.curPreviewEl && this.curDoc && this.options?.syncPreview) {
            syncRemovePreviewCmd(this.curPreviewEl, this.curDoc);
        }

        if (this.lastHitEl && this.curDoc && this.options?.highlightOnMatch)
            this.removeHighlight([this.lastHitEl], this.curDoc);

        this.curPreviewEl = undefined;
        this.lastHitEl = undefined;
        this.curDoc = undefined;
        this.isAccepted = false;
        this.resetId();
    }

    override tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): VertexOnStroke {
        if (this.acceptMarked) return undefined;
        this.curDoc = doc;

        const hitCtx = this.getHitContext(doc, event);
        let hitEl = hitCtx?.hitDetails?.el as StrokeType | undefined;

        if (hitEl && this.options?.cfunc && !this.options.cfunc(hitEl)) {
            // check validity
            hitEl = undefined;
        }

        if (this.lastHitEl && this.lastHitEl !== hitEl && this.options?.highlightOnMatch) {
            this.removeHighlight([this.lastHitEl], doc);
        }

        if (hitEl && this.options?.highlightOnMatch && this.lastHitEl !== hitEl) {
            this.highlight([hitEl], doc);
        }

        this.lastHitEl = hitEl;
        let needSyncPreview = false;

        if (hitEl) {
            // if we hit something
            const pointerPosInGeo = posInGeo(event, doc, true);
            const pos = projectOnEl(hitEl, [pointerPosInGeo.x, pointerPosInGeo.y], doc.rendererCtrl);

            let preview = pVertex(this.curGeneratedId, pos);
            preview.unselectable = true; // newly created preview is trial and should not be hit in hit test

            if (this.options?.tfunc && preview) {
                // do transformation
                preview = this.options.tfunc(hitEl, preview, doc);
            }

            if (preview) {
                this.curPreviewEl = preview;
                needSyncPreview = true;
            }
        } else {
            if (this.curPreviewEl) {
                // if we don't hit anything, return undefined
                // if previously we have a preview point, remove it if we don't hit anything now
                this.removeHighlight([this.curPreviewEl], doc);
                if (this.options?.syncPreview) syncRemovePreviewCmd(this.curPreviewEl, doc);
                this.curPreviewEl = undefined;
            }

            return undefined;
        }

        if (this.options?.autoAcceptOnPointerUp && event.eventType === 'pointerup' && hitEl) {
            this.curPreviewEl.unselectable = false;
            if (this.options?.syncPreview && needSyncPreview) this.syncPreviewCommands(this.curPreviewEl, doc);
            this.accept([hitEl, this.curPreviewEl], doc);
        } else if (this.options?.syncPreview && needSyncPreview) this.syncPreviewCommands(this.curPreviewEl, doc);

        return [hitEl, this.curPreviewEl];
    }

    override clearTrial(doc: GeoDocCtrl) {
        if (this.curPreviewEl && this.options?.syncPreview) {
            syncRemovePreviewCmd(this.curPreviewEl, doc);
            this.curPreviewEl = undefined;
        }

        if (this.lastHitEl && this.options?.highlightOnMatch) this.removeHighlight([this.lastHitEl], doc);
    }
}
