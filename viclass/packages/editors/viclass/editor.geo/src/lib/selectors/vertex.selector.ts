import { ErrorHandlerDecorator, LocatableEvent } from '@viclass/editor.core';
import { syncPotentialSelection, syncRemovePotentialSelection, syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeoRenderElement, RenderVertex } from '../model';
import { pVertex } from '../model/preview.util';
import { GeoDocCtrl, GeoSelectHitContext } from '../objects';
import { posInGeo } from '../tools/tool.utils';
import { ElementSelector, PreviewIds, SelectorOptions } from './selectors';

export type VertexSelectorOptions = SelectorOptions<RenderVertex> & {
    stopSnap?: boolean;

    /**
     * In cases where pointer position is not the position the preview point
     * like, for example,
     *   - we need to select a point on a segment or line, then a projection transformation can be supplied to the selector.
     *
     * The preview is first created, and then the transformation is
     * called to update the preview point accordingly.
     * @param previewEl
     * @returns
     *  - the transfrom render vertex or
     *  - undefined if the selection is rejected, in this case, if the curPreviewEl is previously set, it will be cleared.
     */
    tfunc?: (previewEl: RenderVertex, doc?: GeoDocCtrl) => RenderVertex;

    /**
     * a checking function to determine if a selected vertex is valid.
     * For example if when creating perfect triangle, we select an existing point we need to check if the selected point
     * is indeed within the error threshold.
     * @param el
     * @returns true / false to tell if the element is indeed a valid element.
     */
    cfunc?: (el: RenderVertex) => boolean;
};

/**
 * VertexSelector loops through the elements of a document and check which vertex matches the
 * position of the pointer event.
 *
 * If the options include `preview`, it will check for preview elements.
 * If the options include `renderEl`, it will check for render elements.
 *
 * If both are enabled it will check render element first, then preview elements.
 */
export class VertexSelector extends ElementSelector<RenderVertex, VertexSelectorOptions> {
    /**
     * The current preview element that this selector created.
     * */
    private curPreviewEl?: RenderVertex;
    private lastHitEl?: RenderVertex;
    private curDoc?: GeoDocCtrl;
    private curStickyDoc?: GeoDocCtrl;

    override readonly multiple: boolean = false;

    private filterElementFunc = (el: GeoRenderElement): boolean => {
        // cannot hit the preview point generated by the selector itself,
        // because if there is a preview, it will be selected through trySelect method return.
        if (this.curPreviewEl && el.relIndex == this.curPreviewEl.relIndex) return false;
        if (el.type != 'RenderVertex') return false;

        if (this.options?.refinedFilter) return this.options?.refinedFilter(el as RenderVertex);

        return true;
    };

    private stickyFilterFunc = (el: GeoRenderElement): boolean => {
        return el == this.curStickyEl;
    };

    isAccepted = false;

    constructor(options?: Partial<VertexSelectorOptions>) {
        super();

        const defaultOpt: VertexSelectorOptions = {
            stickyOnMatch: true,
            highlightOnMatch: true,
            stopSnap: false,
            renderEl: true,
            preview: false,
            stickyPTypes: ['touch'], // all pointer types are sticky by default
            autoAcceptOnPointerUp: true,
            genPreview: true,
            syncPreview: true,
            previewQueue: undefined,
        };

        if (options) Object.assign(defaultOpt, options);
        this.options = defaultOpt;

        this.resetId();
    }

    override reset(keepPreview: boolean = false) {
        if (!keepPreview && this.curPreviewEl && this.curDoc && this.options?.syncPreview) {
            syncRemovePreviewCmd(this.curPreviewEl, this.curDoc);
        }

        if (this.lastHitEl && this.curDoc && this.options?.highlightOnMatch)
            this.removeHighlight([this.lastHitEl], this.curDoc);

        if (this.curStickyEl) {
            // if there is a sticky element, and it is not the same as the hit element,
            // we remove the sticky state of the previous sticky element
            this.removeSticky(this.curStickyEl, this.curStickyDoc);
        }

        this.curPreviewEl = undefined;
        this.lastHitEl = undefined;
        this.curDoc = undefined;
        this.isAccepted = false;
        this.resetId();
    }

    override resetId() {
        this.curGeneratedId = PreviewIds.vertex--;
    }

    private getHitContext(doc: GeoDocCtrl, event: LocatableEvent<any>): GeoSelectHitContext | undefined {
        // before checking hit, we save the old filter
        const cachedFilter = doc.editor.filterElementFunc;

        let stickyHitCtx: GeoSelectHitContext | undefined;
        // if there is sticky element, prioritize it
        if (this.curStickyEl) {
            doc.editor.filterElementFunc = this.stickyFilterFunc;
            // if the sticky element has id < 0, it means it is a preview element and should be checked in preview list
            stickyHitCtx = doc.editor.checkHitInternal(doc.layers[0], event, true, this.curStickyEl.relIndex < 0);
        }

        doc.editor.filterElementFunc = this.filterElementFunc;

        let hitCtxOriginal =
            (!stickyHitCtx || !stickyHitCtx.hitDetails) && this.options?.renderEl
                ? doc.editor.checkHitInternal(doc.layers[0], event)
                : undefined;

        // check if the hit el is VALID by calling cFunc if any
        if (hitCtxOriginal?.hitDetails?.el && this.options?.cfunc) {
            if (!this.options.cfunc(hitCtxOriginal.hitDetails.el as RenderVertex)) {
                hitCtxOriginal = undefined; // if the hit el is not valid, we just ignore it
            }
        }

        let hitCtxPreview =
            this.options?.preview &&
            (!stickyHitCtx || !stickyHitCtx.hitDetails) &&
            (!hitCtxOriginal || !hitCtxOriginal.hitDetails)
                ? doc.editor.checkHitInternal(doc.layers[0], event, false, true)
                : undefined;

        if (hitCtxPreview?.hitDetails?.el && this.options?.cfunc) {
            if (!this.options.cfunc(hitCtxPreview.hitDetails.el as RenderVertex)) {
                hitCtxPreview = undefined; // if the hit el is not valid, we just ignore it
            }
        }

        const hitCtx = stickyHitCtx?.hitDetails
            ? stickyHitCtx
            : hitCtxOriginal?.hitDetails
              ? hitCtxOriginal
              : hitCtxPreview;
        doc.editor.filterElementFunc = cachedFilter;

        return hitCtx;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    override tryHit(event: LocatableEvent<any>, doc: GeoDocCtrl): RenderVertex | undefined {
        if (this.acceptMarked) return undefined;

        // TODO: should check doc change, just in case we support multiple doc that are edited at the same time
        // the another doc when the tool which use this selector is still active. We probably should reject this pointer event
        this.curDoc = doc;
        // Implement the logic to select a vertex based on the pointer event
        // This is a placeholder implementation

        const hitCtx = this.getHitContext(doc, event);

        var hitEl = hitCtx?.hitDetails?.el;
        if (hitEl && hitEl.type != 'RenderVertex')
            throw new Error(
                'Unexpected error. The filter function should have filtered out the unwanted element types'
            );

        // check if the hit el is VALID by calling cFunc if any
        if (hitEl && this.options?.cfunc) {
            if (!this.options.cfunc(hitEl as RenderVertex)) {
                hitEl = undefined; // if the hit el is not valid, we just ignore it
            }
        }

        if (hitEl) {
            // if we hit something, it will not be the current preview el generated by this selector
            // it will also be a valid element, because it is a pre-existing element, we don't do any
            // transformation
            if (this.curPreviewEl) {
                // if there is any current preview generated by this selector and has been synchronized, remove it.
                if (this.options?.syncPreview) syncRemovePreviewCmd(this.curPreviewEl, doc);
                this.curPreviewEl = undefined;
            }

            try {
                if (this.options?.highlightOnMatch && this.lastHitEl !== hitEl)
                    this.highlight([hitEl as RenderVertex], doc);
                this.lastHitEl = hitEl as RenderVertex;

                if (this.curStickyEl && this.curStickyEl != hitEl) {
                    // if there is a sticky element, and it is not the same as the hit element,
                    // we remove the sticky state of the previous sticky element
                    this.removeSticky(this.curStickyEl, doc);
                }

                if (this.options?.stickyOnMatch) {
                    if (
                        this.curStickyEl !== hitEl &&
                        (!this.options?.stickyPTypes ||
                            this.options?.stickyPTypes == 'all' ||
                            this.options?.stickyPTypes.includes((event.nativeEvent as PointerEvent).pointerType))
                    ) {
                        this.sticky(hitEl as RenderVertex, doc); // set the sticky state of the hit element
                    }
                }
                return hitEl as RenderVertex;
            } finally {
                if (this.options?.autoAcceptOnPointerUp && event.eventType == 'pointerup')
                    this.checkAndAccept(hitEl, doc);
            }
        } else if (!hitEl) {
            // if we don't hit anything
            if (this.lastHitEl && this.options?.highlightOnMatch) {
                this.removeHighlight([this.lastHitEl], doc);
                this.lastHitEl = undefined;
            }

            if (this.curStickyEl) {
                // if there is a sticky element, we remove it, since we are not hitting any
                // existing element
                this.removeSticky(this.curStickyEl, doc);
            }
        }

        return undefined;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    override tryPreview(event: LocatableEvent<any>, doc: GeoDocCtrl): RenderVertex {
        if (this.acceptMarked) return undefined;
        const pointerPosInGeo = posInGeo(event, doc, this.options?.stopSnap);
        let needSyncPreview = false;
        if (this.options?.genPreview) {
            // if auto gen preview is set
            var preview = pVertex(this.curGeneratedId, [pointerPosInGeo.x, pointerPosInGeo.y, 0]); // generate the preview
            preview.unselectable = true; // before sync, use this

            if (this.options?.tfunc && preview) {
                // do transformation
                preview = this.options.tfunc(preview, doc);
            }

            if (preview && this.options?.cfunc) {
                // check if preview is valid
                if (!this.options.cfunc(preview)) {
                    preview = undefined;
                }
            }

            if (preview != undefined) {
                needSyncPreview = true;
            } else if (this.curPreviewEl && preview == undefined) {
                if (this.options?.syncPreview) syncRemovePreviewCmd(this.curPreviewEl, doc); // else, remove the preview if needed
            }

            this.curPreviewEl = preview;
        } else {
            if (this.curPreviewEl && this.options?.syncPreview) {
                syncRemovePreviewCmd(this.curPreviewEl, doc);
            }
            this.curPreviewEl = undefined;
        }

        // in the end, if not hitting any element, we return the preview element,
        // or if it is not set, return undefined, which means nothing is matched
        // for try selection
        try {
            return this.curPreviewEl;
        } finally {
            if (this.options?.autoAcceptOnPointerUp && event.eventType == 'pointerup') {
                this.curPreviewEl.unselectable = false;
                if (this.options?.syncPreview && needSyncPreview) this.syncPreviewCommands(this.curPreviewEl, doc); // must be before accept
                this.checkAndAccept(this.curPreviewEl, doc);
            } else if (this.options?.syncPreview && needSyncPreview) this.syncPreviewCommands(this.curPreviewEl, doc);
        }
    }

    private removeSticky(el: RenderVertex, doc: GeoDocCtrl): void {
        syncRemovePotentialSelection(el, doc);
        this.curStickyEl = undefined;
    }

    /**
     * Set an element as sticky. This means that the element will be prioritized for checking selection
     * and its selection area will be enlarged. When an element is sticky, moving the pointer inside its selection
     * area will ALWAYS hit the element first.
     *
     * When using with DSL such as and DSL, the dsl must ensure only a single selector being sticky at a time.
     *
     * Moving the pointer out of the sticky element's selection area will remove its stickiness.
     *
     * @param el
     * @param doc
     */
    private sticky(el: GeoRenderElement, doc: GeoDocCtrl): void {
        syncPotentialSelection(el, doc);
        this.curStickyDoc = doc;
        this.curStickyEl = el as RenderVertex;
    }

    private checkAndAccept(el: GeoRenderElement | undefined, doc: GeoDocCtrl) {
        if (!el) return;
        else this.accept(el as RenderVertex, doc);
    }

    override clearTrial(doc: GeoDocCtrl) {
        if (this.curPreviewEl && this.options?.syncPreview) {
            syncRemovePreviewCmd(this.curPreviewEl, doc);
            this.curPreviewEl = undefined;
        }

        if (this.lastHitEl && this.options?.highlightOnMatch) this.removeHighlight([this.lastHitEl], doc);
    }
}

export function vertex(options?: Partial<VertexSelectorOptions>): VertexSelector {
    return new VertexSelector(options);
}
