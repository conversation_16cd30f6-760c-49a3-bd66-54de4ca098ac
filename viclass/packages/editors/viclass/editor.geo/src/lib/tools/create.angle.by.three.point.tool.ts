import { point, Vector, vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    RenderAngle,
    RenderLineSegment,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pAngle, pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { repeat, then, vertex, ThenSelector, vert } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { geoDefaultHandlerFn } from '../error-handler';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './tool.utils';

// Type guard for RenderVertex
function isRenderVertex(obj: any): obj is RenderVertex {
    return obj && Array.isArray(obj.coords);
}

/**
 * Refactored: CreateAngleByThreePointsTool
 * Supports both 3-point and 2-line input using selector DSL and PreviewQueue.
 */
export class CreateAngleByThreePointsTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleByThreePointsTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    renderAngle: RenderAngle | undefined;
    basePoints: RenderVertex[] = [];
    lastVertex: any;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.renderAngle = undefined;
        this.basePoints = [];
        super.resetState();
    }

    get first3Points() {
        const first3 = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: (el: RenderVertex) => !this.basePoints.includes(el),
        });
        return repeat(first3, {
            count: 3,
            onPartialSelection: newSel => {
                const v = isRenderVertex(newSel) ? newSel : vert(newSel);
                this.basePoints.push(v);
                return true;
            },
        });
    }

    private createSelLogic() {
        this.lastVertex = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: (el: RenderVertex) => !this.basePoints.includes(el),
        });
        this.selLogic = then([this.first3Points, this.lastVertex], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [firstThree, directionPoint] = selector.selected;
                const points = [firstThree[0], firstThree[1], firstThree[2]] as RenderVertex[];
                this.performConstructionFromPoints(points, directionPoint, doc);
            },
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }
        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    private doTrySelection(event: any, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected) {
            if (selected.length === 2 && Array.isArray(selected[0]) && selected[0].length === 3) {
                // Đủ 4 điểm: 3 điểm tạo góc, 1 điểm chọn hướng
                const [p1, p2, p3] = (selected[0] as any[]).map(s =>
                    isRenderVertex(s) ? s : vert(s)
                ) as RenderVertex[];
                if (!isRenderVertex(selected[1])) return; // Only preview if last is vertex
                const p4 = selected[1] as RenderVertex;
                this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p1, p2));
                this.pQ.add(pLine(ctrl, -23, RenderLineSegment, p2, p3));
                // Xác định hướng góc
                const fp1 = point(p1.coords[0], p1.coords[1]);
                const fp2 = point(p2.coords[0], p2.coords[1]);
                const fp3 = point(p3.coords[0], p3.coords[1]);
                const fp4 = point(p4.coords[0], p4.coords[1]);
                const v1 = vector(fp2, fp1);
                const v2 = vector(fp2, fp3);
                const v4 = vector(fp2, fp4);
                const angle12 = v1.angleTo(v2);
                const angle14 = v1.angleTo(v4);
                if (
                    (angle12 > 0 && angle14 > 0 && angle14 < angle12) ||
                    (angle12 < 0 && angle14 < 0 && angle14 > angle12)
                ) {
                    const line1 = pLine(ctrl, -25, RenderLineSegment, p2, p1);
                    const line2 = pLine(ctrl, -26, RenderLineSegment, p2, p3);
                    this.pQ.add(pAngle(ctrl, -24, line1, line2));
                } else {
                    const line1 = pLine(ctrl, -25, RenderLineSegment, p2, p3);
                    const line2 = pLine(ctrl, -26, RenderLineSegment, p2, p1);
                    this.pQ.add(pAngle(ctrl, -24, line1, line2));
                }
            } else if (selected.length === 1 && Array.isArray(selected[0]) && selected[0].length === 3) {
                // Đủ 3 điểm: preview hai cạnh
                const [p1, p2, p3] = (selected[0] as any[]).map(s =>
                    isRenderVertex(s) ? s : vert(s)
                ) as RenderVertex[];
                this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p1, p2));
                this.pQ.add(pLine(ctrl, -23, RenderLineSegment, p2, p3));
            } else if (selected.length === 1 && Array.isArray(selected[0]) && selected[0].length === 2) {
                // Đủ 2 điểm: preview đoạn thẳng
                const [p1, p2] = (selected[0] as any[]).map(s => (isRenderVertex(s) ? s : vert(s))) as RenderVertex[];
                this.pQ.add(pLine(ctrl, -21, RenderLineSegment, p1, p2));
            }
        }
        this.pQ.flush(ctrl);
    }

    private getAngleDirection(
        points: RenderVertex[],
        directionPoint: RenderVertex
    ): {
        startPoint: RenderVertex;
        endPoint: RenderVertex;
    } {
        const [p1, p2, p3] = points;
        const fp = points.map(p => point(p.coords[0], p.coords[1]));
        const fp4 = point(directionPoint.coords[0], directionPoint.coords[1]);
        const v1 = vector(fp[1], fp[0]);
        const v2 = vector(fp[1], fp[2]);
        const v4 = vector(fp[1], fp4);
        const angle12 = v1.angleTo(v2);
        const angle14 = v1.angleTo(v4);
        if ((angle12 > 0 && angle14 > 0 && angle14 < angle12) || (angle12 < 0 && angle14 < 0 && angle14 > angle12)) {
            return {
                startPoint: p1,
                endPoint: p3,
            };
        } else {
            return {
                startPoint: p3,
                endPoint: p1,
            };
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromPoints(
        points: (RenderVertex | any)[],
        directionPoint: RenderVertex | any,
        ctrl: GeoDocCtrl
    ) {
        try {
            // Ensure all are RenderVertex
            let pts = points.map(p => (isRenderVertex(p) ? p : vert(p)));
            const dirPt = isRenderVertex(directionPoint) ? directionPoint : vert(directionPoint);

            const [p1, p2, p3] = pts;

            // Calculate direction and swap if needed
            const { startPoint, endPoint } = this.getAngleDirection(pts, dirPt);
            pts = [startPoint, p2, endPoint];

            const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
            // Request names for points
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Tên góc',
                        originElement: pts,
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) return;

            // Assign names and build construction points for unnamed points
            const constructionPoints: GeoElConstructionRequest[] = pts
                .map((p, i) => {
                    if (!p.name) {
                        p.name = inputPointNames[i];
                        return buildPointConstruction(p.name, { x: p.coords[0], y: p.coords[1] });
                    }
                    return null;
                })
                .filter(Boolean) as GeoElConstructionRequest[];

            const angleName: string = pts.map(p => p.name).join('');
            const constructionAngle = this.buildAngleConstruction(
                angleName,
                pts[0].name,
                pts[1].name,
                pts[2].name,
                pts[1].name.toUpperCase() < pts[0].name.toUpperCase() ? 1 : -1,
                pts[1].name.toUpperCase() < pts[2].name.toUpperCase() ? 1 : -1
            );

            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo góc',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    const constructResponse = await constructExec(() =>
                        this.editor.geoGateway.construct(ctrl.state.globalId, [
                            ...constructionPoints.map(c => <ConstructionRequest>{ construction: c }),
                            { construction: constructionAngle },
                        ])
                    );
                    await syncRenderCommands(constructResponse.render, ctrl);
                    await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                }
            );
            this.resetState();
        } catch (err: any) {
            this.resetState();
            console.error(err);
            throw err;
        }
    }

    private buildAngleConstruction(
        name: string,
        firstPointName: string,
        secondPointName: string,
        threePointName: string,
        startLineDirection: number,
        endLineDirection: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Angle/AngleEC', 'Angle', 'FromThreePoints');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: firstPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: secondPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: threePointName,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: [startLineDirection, endLineDirection],
                    },
                },
            },
        ];
        return construction;
    }
}
