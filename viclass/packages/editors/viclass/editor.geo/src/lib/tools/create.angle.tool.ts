import Flatten from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    KeyboardHandlingItem,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncPreviewCommands, syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderLine,
    RenderVertex,
} from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine, nthDirectionOnLine } from '../nth.direction';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewVertexRenderProp,
    handleIfPointerNotInError,
    isElementLine,
    pickPointName,
    requestElementNames,
} from './tool.utils';
import point = Flatten.point;
import line = Flatten.line;
import vector = Flatten.vector;
import circle = Flatten.circle;

type LineElement = RenderLine;

/**
 * Tool to create an angle from two line segments or two lines.
 * <AUTHOR>
 */
export class CreateAngleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleTool';

    private selectedPoints: RenderVertex[] = [];
    private firstLine: LineElement | undefined;
    private secondLine: LineElement | undefined;
    private isClockwise: boolean = false;
    private isPointerActive: boolean = false;

    protected override readonly filterElementFunc = (element: GeoRenderElement) =>
        isElementLine(element) ||
        (element.type === 'RenderVertex' && this.selectedPoints.every(p => p.relIndex !== element.relIndex));

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.registerPointerHandling(
            // pointer down
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
            // pointer up (confirm)
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // pointer move/preview
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['nokey'] },
            { event: 'pointermove', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 },
            // shift key support
            { event: 'pointerdown', keys: ['shift'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', keys: ['shift'], button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['shift'] }
        );
        this.registerKeyboardHandling(
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keyup';
            })(['shift']),
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keydown';
            })(['shift'])
        );
    }

    override resetState() {
        this.selectedPoints = [];
        this.firstLine = undefined;
        this.secondLine = undefined;
        this.isPointerActive = false;
        super.resetState();
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        event = super.handleKeyboardEvent(event);
        if (event.nativeEvent.key === 'Shift') {
            this.isClockwise = event.eventType === 'keydown';
            if (this.lastPointerMove) this.onPointerMove(this.lastPointerMove);
        }
        return event;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown':
                this.onPointerDown(event);
                break;
            case 'pointerup':
                this.onPointerUp(event);
                break;
            case 'pointermove':
                this.onPointerMove(event);
                break;
            default:
                break;
        }
        return event;
    }

    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerActive) return;
        this.isPointerActive = true;
    }

    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerActive) return;
        this.isPointerActive = false;

        // Step transition rules
        if (this.firstLine && this.secondLine) {
            await this.handleFinalStep(event);
        } else if (this.selectedPoints.length === 0 && !this.firstLine) {
            await this.handleFirstSelection(event);
        } else if (
            (this.selectedPoints.length === 1 && !this.firstLine) ||
            (this.selectedPoints.length === 0 && this.firstLine)
        ) {
            await this.handleSecondSelection(event);
        } else if (
            (this.selectedPoints.length === 2 && !this.secondLine) ||
            (this.selectedPoints.length === 1 && this.firstLine)
        ) {
            await this.handleThirdSelection(event);
        } else if (this.selectedPoints.length === 3 && !this.secondLine) {
            await this.handleFourthSelection(event);
        }
    }

    // --- UTILITIES ---
    private createPreviewVertex(coords: [number, number], relIndex: number = -10): RenderVertex {
        return {
            relIndex,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            name: undefined,
            coords: [coords[0], coords[1]],
            usable: true,
            valid: true,
        };
    }

    // private createPreviewLine(
    //     startCoords: [number, number],
    //     endCoords: [number, number],
    //     relIndex: number,
    //     name?: string
    // ): PreviewLine {
    //     return {
    //         relIndex,
    //         renderProp: buildPreviewLineSegmentRenderProp(),
    //         name: name ?? '',
    //         type: 'RenderLineSegment',
    //         elType: 'LineSegment',
    //         startPoint: startCoords,
    //         endPoint: endCoords,
    //         usable: true,
    //         valid: true,
    //     };
    // }

    private findExistingLineByPointNames(renderer: any, name1: string, name2: string): RenderLine | undefined {
        return renderer
            .allElements()
            .find((e: GeoRenderElement) => e.type === 'RenderLine' && [name1, name2].includes(e.name)) as
            | RenderLine
            | undefined;
    }

    // --- STEPS HANDLING ---

    /**
     * Step 1: Select the first point or line segment
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstSelection(event: GeoPointerEvent) {
        const { ctrl, pos, hitCtx, hitEl } = this.posAndCtrl(event);

        if (hitEl) {
            this.editor.selectElement(hitCtx);
            if (hitEl.type === 'RenderVertex') {
                this.selectedPoints.push(hitEl as RenderVertex);
            } else if (isElementLine(hitEl)) {
                this.firstLine = hitEl as RenderLine;
            } else {
                return;
            }
            ctrl.editor.selectElement(hitCtx, true);
        } else {
            const previewPoint = this.createPreviewVertex([pos.x, pos.y], -10);
            this.selectedPoints.push(previewPoint);
            syncPreviewCommands(previewPoint, ctrl);
            this.started = true;
        }

        if (!this.firstLine) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                el.type === 'RenderVertex' && this.selectedPoints.every(p => p.relIndex !== el.relIndex);
        } else {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                el.type === 'RenderLine' ||
                el.type === 'RenderLineSegment' ||
                el.type === 'RenderRay' ||
                (el.type === 'RenderVector' && this.firstLine.relIndex !== el.relIndex) ||
                (el.type === 'RenderVertex' && this.selectedPoints.every(p => p.relIndex !== el.relIndex));
        }
    }

    /**
     * Step 2: Select the second point or line segment (if the first point is already selected)
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondSelection(event: GeoPointerEvent) {
        const { ctrl, pos, hitCtx, hitEl, renderer } = this.posAndCtrl(event);

        if (hitEl) {
            ctrl.editor.selectElement(hitCtx, true);
            if (hitEl.type === 'RenderVertex') {
                this.selectedPoints.push(hitEl as RenderVertex);
            } else if (isElementLine(hitEl)) {
                this.secondLine = hitEl as RenderLine;
                this.editor.filterElementFunc = () => false;
                return;
            } else {
                return;
            }
        } else {
            const previewPoint = this.createPreviewVertex([pos.x, pos.y], -11);
            this.selectedPoints.push(previewPoint);
            syncPreviewCommands(previewPoint, ctrl);
        }

        if (!this.firstLine && this.selectedPoints.length === 2) {
            const [pt1, pt2] = this.selectedPoints;
            const name1 = `${pt1.name ?? ''}${pt2.name ?? ''}`;
            const name2 = `${pt2.name ?? ''}${pt1.name ?? ''}`;
            const foundLine = this.findExistingLineByPointNames(renderer, name1, name2);
            if (foundLine && foundLine.name) {
                this.firstLine = foundLine;
            } else {
                // Create a temporary line segment
                // this.firstLine = {
                //     relIndex: undefined,
                //     renderProp: buildPreviewLineSegmentRenderProp(),
                //     name: pt1.name && pt2.name ? name1 : undefined,
                //     type: 'RenderLineSegment',
                //     elType: 'LineSegment',
                //     startPointIdx: pt1.relIndex,
                //     endPointIdx: pt2.relIndex,
                //     vector: [pt2.coords[0] - pt1.coords[0], pt2.coords[1] - pt1.coords[1], 0.0],
                //     usable: true,
                //     valid: true,
                // };
                // const previewLine = this.createPreviewLine(
                //     pt1.coords as [number, number],
                //     pt2.coords as [number, number],
                //     -20,
                //     this.firstLine.name
                // );
                // syncPreviewCommands(previewLine, ctrl);
            }
        }

        if (this.selectedPoints.length === 1) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                el.type === 'RenderVertex' && this.selectedPoints[0].relIndex !== el.relIndex;
        } else if (this.selectedPoints.length === 2) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                el.type === 'RenderLine' ||
                el.type === 'RenderLineSegment' ||
                el.type === 'RenderRay' ||
                (el.type === 'RenderVector' && this.firstLine?.relIndex !== el.relIndex) ||
                el.type === 'RenderVertex';
        }
    }

    /**
     * Step 3: Similar to Step 2 for the second line/element of the angle (can be taken from object or temporary point)
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleThirdSelection(event: GeoPointerEvent) {
        const { ctrl, pos, hitCtx, hitEl, renderer } = this.posAndCtrl(event);

        if (hitEl) {
            ctrl.editor.selectElement(hitCtx, true);
            if (hitEl.type === 'RenderVertex') {
                this.selectedPoints.push(hitEl as RenderVertex);
            } else if (isElementLine(hitEl)) {
                this.secondLine = hitEl as RenderLine;
                this.editor.filterElementFunc = () => false;
                return;
            } else {
                return;
            }
        } else {
            const previewPoint = this.createPreviewVertex([pos.x, pos.y], -12);
            this.selectedPoints.push(previewPoint);
            syncPreviewCommands(previewPoint, ctrl);
        }

        if (this.selectedPoints.length === 2) {
            const [pt1, pt2] = this.selectedPoints;
            const name1 = `${pt1.name ?? ''}${pt2.name ?? ''}`;
            const name2 = `${pt2.name ?? ''}${pt1.name ?? ''}`;
            const foundLine = this.findExistingLineByPointNames(renderer, name1, name2);
            if (foundLine && foundLine.name) {
                this.secondLine = foundLine;
            } else {
                // this.secondLine = {
                //     relIndex: undefined,
                //     renderProp: buildPreviewLineSegmentRenderProp(),
                //     name: pt1.name && pt2.name ? name1 : undefined,
                //     type: 'RenderLineSegment',
                //     elType: 'LineSegment',
                //     startPointIdx: pt1.relIndex,
                //     endPointIdx: pt2.relIndex,
                //     vector: [pt2.coords[0] - pt1.coords[0], pt2.coords[1] - pt1.coords[1], 0.0],
                //     usable: true,
                //     valid: true,
                // };
                // const previewLine = this.createPreviewLine(
                //     pt1.coords as [number, number],
                //     pt2.coords as [number, number],
                //     -21,
                //     this.secondLine.name
                // );
                // syncPreviewCommands(previewLine, ctrl);
            }

            this.editor.filterElementFunc = () => false;
        }
    }

    /**
     * Step 4: Only in case of selecting 4 points to create two temporary line segments
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFourthSelection(event: GeoPointerEvent) {
        const { ctrl, pos, hitCtx, hitEl, renderer } = this.posAndCtrl(event);

        if (hitEl) {
            ctrl.editor.selectElement(hitCtx, true);
            if (hitEl.type === 'RenderVertex') {
                this.selectedPoints.push(hitEl as RenderVertex);
            } else {
                return;
            }
        } else {
            const previewPoint = this.createPreviewVertex([pos.x, pos.y], -13);
            this.selectedPoints.push(previewPoint);
            syncPreviewCommands(previewPoint, ctrl);
        }

        const p1 = this.selectedPoints[2];
        const p2 = this.selectedPoints[3];
        const name1 = `${p1.name ?? ''}${p2.name ?? ''}`;
        const name2 = `${p2.name ?? ''}${p1.name ?? ''}`;
        const foundLine = this.findExistingLineByPointNames(renderer, name1, name2);
        if (foundLine && foundLine.name) {
            this.secondLine = foundLine;
        } else {
            // this.secondLine = {
            //     relIndex: undefined,
            //     renderProp: buildPreviewLineSegmentRenderProp(),
            //     name: p1.name && p2.name ? name1 : undefined,
            //     type: 'RenderLineSegment',
            //     elType: 'LineSegment',
            //     startPointIdx: p1.relIndex,
            //     endPointIdx: p2.relIndex,
            //     vector: [p2.coords[0] - p1.coords[0], p2.coords[1] - p1.coords[1], 0.0],
            //     usable: true,
            //     valid: true,
            // };
            // const previewLine = this.createPreviewLine(
            //     p1.coords as [number, number],
            //     p2.coords as [number, number],
            //     -21,
            //     this.secondLine.name
            // );
            // syncPreviewCommands(previewLine, ctrl);
        }

        this.editor.filterElementFunc = () => false;
    }

    /**
     * Final Step: Confirm and send the request to create the angle
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFinalStep(event: GeoPointerEvent) {
        const { ctrl, pos, docGlobalId } = this.posAndCtrl(event);
        const currentPos = [pos.x, pos.y, 0.0] as [number, number, number];

        const constructionPoints: GeoElConstructionRequest[] = [];

        // ---- Get or initialize the names of the two endpoints of each line segment ----
        // helper to get names, create points if not present
        async function ensureLinePoints(
            ctxCtrl: any,
            line: LineElement,
            fallbackPoints: RenderVertex[],
            prefix: string,
            excludeNames: string[] = []
        ): Promise<{ names: string[]; start: RenderVertex; end: RenderVertex; lineName: string; lineType: string }> {
            let start: RenderVertex,
                end: RenderVertex,
                names: string[] = [];
            if (line.name) {
                start = ctxCtrl.rendererCtrl.elementAt(line.startPointIdx) as RenderVertex;
                end = ctxCtrl.rendererCtrl.elementAt(line.endPointIdx) as RenderVertex;
                names = [start.name, end.name];
            } else {
                start = fallbackPoints.find(p => p.relIndex === line.startPointIdx) as RenderVertex;
                end = fallbackPoints.find(p => p.relIndex === line.endPointIdx) as RenderVertex;
                const originPoints = [start, end];
                names = await this.requestPointName(ctxCtrl, [
                    {
                        objName: prefix,
                        originElement: originPoints,
                        pickName: pickPointName,
                        namesToAvoid: excludeNames,
                    },
                ]);
                if (!names.length) throw 'ABORT';
                for (let i = 0; i < 2; i++) {
                    if (!originPoints[i].name) {
                        originPoints[i].name = names[i];
                        const cp = buildPointConstruction(names[i], {
                            x: originPoints[i].coords[0],
                            y: originPoints[i].coords[1],
                        });
                        constructionPoints.push(cp);
                    }
                }
            }
            return {
                names,
                start,
                end,
                lineName: names.length ? `${names[0]}${names[1]}` : line.name,
                lineType: line.elType,
            };
        }

        // Get names for the two line segments
        let line1Result, line2Result;
        try {
            line1Result = await ensureLinePoints.call(
                this,
                ctrl,
                this.firstLine,
                this.selectedPoints,
                'Line Segment 1'
            );
            line2Result = await ensureLinePoints.call(
                this,
                ctrl,
                this.secondLine,
                this.selectedPoints,
                'Line Segment 2',
                [...(line1Result?.names || [])]
            );
        } catch {
            this.resetState();
            return;
        }

        // ---- Determine the intersection point of the two line segments (angle center) ----
        const start1 = line1Result.start;
        const vec1 = this.firstLine.vector;
        const start2 = line2Result.start;
        const vec2 = this.secondLine.vector;
        const flattenLine1 = line(point(start1.coords[0], start1.coords[1]), vector([-vec1[1], vec1[0]]));
        const flattenLine2 = line(point(start2.coords[0], start2.coords[1]), vector([-vec2[1], vec2[0]]));
        const intersection = flattenLine1.intersect(flattenLine2)[0];
        if (!intersection) return;
        const angleCenter: [number, number] = [intersection.x, intersection.y];
        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;

        // ---- Get the name of the intersection point (if existing, retrieve it) ----
        const sharedNames = line1Result.names.filter((nm: any) => line2Result.names.includes(nm));
        let centerName: string = sharedNames.length ? sharedNames[0] : '';
        if (!centerName) {
            const vertexAtIntersection: RenderVertex = this.createPreviewVertex([intersection.x, intersection.y], -12);
            const nameInputs = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Angle Point',
                        originElement: [vertexAtIntersection],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];
            if (!nameInputs.length) {
                this.resetState();
                return;
            }
            centerName = nameInputs[0];
        }

        // ---- Calculate direction & adjust start/end if needed ----
        // Use vector-based direction logic from CreateAngleByThreePointsTool
        const p1 = {
            coords: [line1Result.start.coords[0], line1Result.start.coords[1]]
        };
        const p2 = {
            coords: [intersection.x, intersection.y]
        };
        const p3 = {
            coords: [line2Result.end.coords[0], line2Result.end.coords[1]]
        };
        // Use the current pointer position as the direction point
        const directionPoint = { coords: [pos.x, pos.y] };
        // Build vectors
        const fp = [p1, p2, p3].map(p => point(p.coords[0], p.coords[1]));
        const fp4 = point(directionPoint.coords[0], directionPoint.coords[1]);
        const v1 = vector(fp[1], fp[0]);
        const v2 = vector(fp[1], fp[2]);
        const v4 = vector(fp[1], fp4);
        const angle12 = v1.angleTo(v2);
        const angle14 = v1.angleTo(v4);
        let needSwap = false;
        if ((angle12 > 0 && angle14 > 0 && angle14 < angle12) || (angle12 < 0 && angle14 < 0 && angle14 > angle12)) {
            needSwap = false;
        } else {
            needSwap = true;
        }
        let lineStartName = line1Result.lineName;
        let lineStartType = line1Result.lineType;
        let lineEndName = line2Result.lineName;
        let lineEndType = line2Result.lineType;
        let startDirection = 1;
        let endDirection = 1;
        if (needSwap) {
            [lineStartName, lineEndName] = [lineEndName, lineStartName];
            [lineStartType, lineEndType] = [lineEndType, lineStartType];
            [startDirection, endDirection] = [endDirection, startDirection];
        }
        if (this.isClockwise) {
            [lineStartName, lineEndName] = [lineEndName, lineStartName];
            [lineStartType, lineEndType] = [lineEndType, lineStartType];
            [startDirection, endDirection] = [endDirection, startDirection];
        }
        // ---- Send request to create angle ----
        const angleConstruction = this.buildAngleConstruction(
            lineStartName,
            lineStartType,
            lineEndName,
            lineEndType,
            centerName,
            startDirection,
            endDirection
        );

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Creating angle',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docGlobalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        { construction: angleConstruction },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    // --- Handle pointer move to draw preview for the tool ---
    private onPointerMove(event: GeoPointerEvent) {
        if (!this.selectedPoints.length && !this.firstLine && !this.secondLine) return;
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    private processPointerMove(event: GeoPointerEvent) {
        const { ctrl, pos } = this.posAndCtrl(event, !!this.firstLine && !!this.secondLine);
        const current: [number, number, number] = [pos.x, pos.y, 0.0];

        // No line segments yet, just drag preview between two points
        if (!this.firstLine) {
            if (this.selectedPoints.length === 1) {
                // const v1 = this.selectedPoints[0].coords;
                // const previewLine = this.createPreviewLine(v1 as [number, number], [pos.x, pos.y], -20);
                // syncPreviewCommands(previewLine, ctrl);
            }
            return;
        }

        // First line segment exists, drag preview for second line segment
        if (!this.secondLine) {
            if (this.selectedPoints.length === 1 || this.selectedPoints.length === 3) {
                // const v1 = this.selectedPoints[this.selectedPoints.length - 1].coords;
                // const previewLine = this.createPreviewLine(v1 as [number, number], [pos.x, pos.y], -21);
                // syncPreviewCommands(previewLine, ctrl);
            }
            return;
        }

        // Two line segments ready, preview the angle
        const pStart1 = ctrl.rendererCtrl.elementAt(this.firstLine.startPointIdx) as RenderVertex;
        const pStart2 = ctrl.rendererCtrl.elementAt(this.secondLine.startPointIdx) as RenderVertex;
        let vec1 = this.firstLine.vector;
        let vec2 = this.secondLine.vector;
        const flattenLine1 = line(point(pStart1.coords[0], pStart1.coords[1]), vector([-vec1[1], vec1[0]]));
        const flattenLine2 = line(point(pStart2.coords[0], pStart2.coords[1]), vector([-vec2[1], vec2[0]]));
        const intersection = flattenLine1.intersect(flattenLine2)[0];
        if (!intersection) return;
        const intersectionCoord: [number, number] = [intersection.x, intersection.y];

        const face1 = nthDirectionByLine(vec1, intersectionCoord, current);
        const face2 = nthDirectionByLine(vec2, intersectionCoord, current);

        const testCircle = circle(intersection, 5);
        const circleIntersects1 = testCircle.intersect(flattenLine1);
        const circleIntersects2 = testCircle.intersect(flattenLine2);

        let pointOnLine1: Flatten.Point = circleIntersects1[0];
        if (
            nthDirectionByLine(vec2, intersectionCoord, [circleIntersects1[0].x, circleIntersects1[0].y, 0]) !== face2
        ) {
            pointOnLine1 = circleIntersects1[1];
        }

        let pointOnLine2: Flatten.Point = circleIntersects2[0];
        if (
            nthDirectionByLine(vec1, intersectionCoord, [circleIntersects2[0].x, circleIntersects2[0].y, 0]) !== face1
        ) {
            pointOnLine2 = circleIntersects2[1];
        }

        let direction1 =
            nthDirectionOnLine(vec1, intersectionCoord, [intersectionCoord, [pointOnLine1.x, pointOnLine1.y]]) === 1
                ? 1
                : -1;
        let direction2 =
            nthDirectionOnLine(vec2, intersectionCoord, [intersectionCoord, [pointOnLine2.x, pointOnLine2.y]]) === 1
                ? 1
                : -1;

        const vector1 = vector(vec1[0] * direction1, vec1[1] * direction1);
        const vector2 = vector(vec2[0] * direction2, vec2[1] * direction2);

        const swapNeeded = vector1.angleTo(vector2) > vector2.angleTo(vector1);
        if (swapNeeded) {
            [vec1, vec2] = [vec2, vec1];
            [direction1, direction2] = [direction2, direction1];
        }
        if (this.isClockwise) {
            [vec1, vec2] = [vec2, vec1];
            [direction1, direction2] = [direction2, direction1];
        }

        // const anglePreview: PreviewAngle = {
        //     relIndex: -30,
        //     name: '',
        //     type: 'RenderAngle',
        //     elType: 'Angle',
        //     anglePoint: intersectionCoord,
        //     vStart: [vec1[0] * direction1, vec1[1] * direction1],
        //     vEnd: [vec2[0] * direction2, vec2[1] * direction2],
        //     renderProp: buildPreviewAngleRenderProp(),
        //     usable: true,
        //     valid: true,
        //     degree: vector1.angleTo(vector2),
        // };
        // syncPreviewCommands(anglePreview, ctrl);
    }

    private buildAngleConstruction(
        lineStartName: string,
        lineStartType: string,
        lineEndName: string,
        lineEndType: string,
        anglePointName: string,
        directionStart: number,
        directionEnd: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Angle/AngleEC', 'Angle', 'FromLinesAndDirection');
        construction.name = anglePointName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineEnd',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineEndName,
                    },
                },
                dataTypes: {
                    name: lineEndType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-DirectionOfLineStart',
                params: {
                    value: {
                        type: 'singleValue',
                        value: directionStart,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-DirectionOfLineEnd',
                params: {
                    value: {
                        type: 'singleValue',
                        value: directionEnd,
                    },
                },
            },
        ];

        return construction;
    }
}
