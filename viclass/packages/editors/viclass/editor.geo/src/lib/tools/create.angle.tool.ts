import Flatten from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    RenderAngle,
    RenderLine,
    RenderLineSegment,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pAngle, pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { or, repeat, then, vertex, stroke, ThenSelector, vert } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    pickPointName,
    requestElementNames,
} from './tool.utils';
import point = Flatten.point;
import line = Flatten.line;
import vector = Flatten.vector;

// Type guard for RenderVertex
function isRenderVertex(obj: any): obj is RenderVertex {
    return obj && Array.isArray(obj.coords);
}

/**
 * Unified tool to create an angle from three points or two lines.
 * Supports both point-based and line-based angle creation using selector DSL.
 * <AUTHOR>
 */
export class CreateAngleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    renderAngle: RenderAngle | undefined;
    basePoints: RenderVertex[] = [];
    baseLines: RenderLine[] = [];
    lastVertex: any;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.renderAngle = undefined;
        this.basePoints = [];
        this.baseLines = [];
        super.resetState();
    }

    /**
     * Creates the selection logic that supports both point-based and line-based angle creation.
     * The tool will automatically detect the creation mode based on user selections.
     */
    private createSelLogic() {
        // Create a flexible selector that can handle both points and lines
        const flexibleSelector = or(
            [
                // Option 1: Three points for point-based angle creation
                repeat(
                    vertex({
                        previewQueue: this.pQ,
                        cursor: this.pointerHandler.cursor,
                        refinedFilter: (el: RenderVertex) => !this.basePoints.includes(el),
                    }),
                    {
                        count: 3,
                        onPartialSelection: newSel => {
                            const v = isRenderVertex(newSel) ? newSel : vert(newSel);
                            this.basePoints.push(v);
                            return true;
                        },
                    }
                ),
                // Option 2: Two lines for line-based angle creation
                repeat(
                    stroke({
                        selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                        previewQueue: this.pQ,
                        cursor: this.pointerHandler.cursor,
                        refinedFilter: (el: RenderLine) => !this.baseLines.includes(el),
                    }),
                    {
                        count: 2,
                        onPartialSelection: newSel => {
                            const line = newSel as RenderLine;
                            this.baseLines.push(line);
                            return true;
                        },
                    }
                ),
            ],
            { flatten: true }
        );

        // Direction point selector for determining angle orientation
        this.lastVertex = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: (el: RenderVertex) =>
                !this.basePoints.includes(el) &&
                !this.baseLines.some(line => line.startPointIdx === el.relIndex || line.endPointIdx === el.relIndex),
        });

        // Main selection logic: first select elements, then direction point
        this.selLogic = then([flexibleSelector, this.lastVertex], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [elements, directionPoint] = selector.selected;
                this.performConstruction(elements, directionPoint, doc);
            },
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }
        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    private doTrySelection(event: any, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected) {
            this.handlePreview(selected, ctrl);
        }
        this.pQ.flush(ctrl);
    }

    private handlePreview(selected: any, ctrl: GeoDocCtrl) {
        if (selected.length === 2) {
            // Check if we have elements + direction point
            const [elements, directionPoint] = selected;

            if (Array.isArray(elements)) {
                if (elements.length === 3 && elements.every(el => isRenderVertex(el) || vert(el))) {
                    // Three points selected - preview angle from points
                    this.previewAngleFromPoints(elements, directionPoint, ctrl);
                } else if (elements.length === 2 && elements.every(el => isElementLine(el))) {
                    // Two lines selected - preview angle from lines
                    this.previewAngleFromLines(elements, directionPoint, ctrl);
                }
            }
        } else if (selected.length === 1 && Array.isArray(selected[0])) {
            // Partial selection - show preview lines/points
            const elements = selected[0];
            if (elements.length === 3 && elements.every(el => isRenderVertex(el) || vert(el))) {
                // Three points - preview the two line segments
                const [p1, p2, p3] = elements.map(el => (isRenderVertex(el) ? el : vert(el)));
                this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p1, p2));
                this.pQ.add(pLine(ctrl, -23, RenderLineSegment, p2, p3));
            } else if (elements.length === 2) {
                if (elements.every(el => isRenderVertex(el) || vert(el))) {
                    // Two points - preview line segment
                    const [p1, p2] = elements.map(el => (isRenderVertex(el) ? el : vert(el)));
                    this.pQ.add(pLine(ctrl, -21, RenderLineSegment, p1, p2));
                } else if (elements.every(el => isElementLine(el))) {
                    // Two lines selected - no additional preview needed
                }
            }
        }
    }

    private previewAngleFromPoints(points: any[], directionPoint: any, ctrl: GeoDocCtrl) {
        const [p1, p2, p3] = points.map(p => (isRenderVertex(p) ? p : vert(p))) as RenderVertex[];
        if (!isRenderVertex(directionPoint)) return;
        const p4 = directionPoint as RenderVertex;

        this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p1, p2));
        this.pQ.add(pLine(ctrl, -23, RenderLineSegment, p2, p3));

        // Determine angle direction
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]);
        const fp3 = point(p3.coords[0], p3.coords[1]);
        const fp4 = point(p4.coords[0], p4.coords[1]);
        const v1 = vector(fp2, fp1);
        const v2 = vector(fp2, fp3);
        const v4 = vector(fp2, fp4);
        const angle12 = v1.angleTo(v2);
        const angle14 = v1.angleTo(v4);

        if ((angle12 > 0 && angle14 > 0 && angle14 < angle12) || (angle12 < 0 && angle14 < 0 && angle14 > angle12)) {
            const line1 = pLine(ctrl, -25, RenderLineSegment, p2, p1);
            const line2 = pLine(ctrl, -26, RenderLineSegment, p2, p3);
            this.pQ.add(pAngle(ctrl, -24, line1, line2));
        } else {
            const line1 = pLine(ctrl, -25, RenderLineSegment, p2, p3);
            const line2 = pLine(ctrl, -26, RenderLineSegment, p2, p1);
            this.pQ.add(pAngle(ctrl, -24, line1, line2));
        }
    }

    private previewAngleFromLines(lines: RenderLine[], directionPoint: any, ctrl: GeoDocCtrl) {
        const [line1, line2] = lines;
        if (!isRenderVertex(directionPoint)) return;

        // Calculate intersection point of the two lines
        const start1 = ctrl.rendererCtrl.elementAt(line1.startPointIdx) as RenderVertex;
        const start2 = ctrl.rendererCtrl.elementAt(line2.startPointIdx) as RenderVertex;
        const vec1 = line1.vector;
        const vec2 = line2.vector;

        const flattenLine1 = line(point(start1.coords[0], start1.coords[1]), vector([-vec1[1], vec1[0]]));
        const flattenLine2 = line(point(start2.coords[0], start2.coords[1]), vector([-vec2[1], vec2[0]]));
        const intersection = flattenLine1.intersect(flattenLine2)[0];

        if (intersection) {
            // Create preview angle at intersection
            const previewLine1 = pLine(
                ctrl,
                -25,
                RenderLineSegment,
                { coords: [intersection.x, intersection.y] } as RenderVertex,
                { coords: [intersection.x + vec1[0] * 50, intersection.y + vec1[1] * 50] } as RenderVertex
            );
            const previewLine2 = pLine(
                ctrl,
                -26,
                RenderLineSegment,
                { coords: [intersection.x, intersection.y] } as RenderVertex,
                { coords: [intersection.x + vec2[0] * 50, intersection.y + vec2[1] * 50] } as RenderVertex
            );
            this.pQ.add(pAngle(ctrl, -24, previewLine1, previewLine2));
        }
    }

    /**
     * Main construction method that handles both point-based and line-based angle creation
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(elements: any, directionPoint: any, ctrl: GeoDocCtrl) {
        try {
            if (
                Array.isArray(elements) &&
                elements.length === 3 &&
                elements.every(el => isRenderVertex(el) || vert(el))
            ) {
                // Point-based angle creation
                await this.performConstructionFromPoints(elements, directionPoint, ctrl);
            } else if (Array.isArray(elements) && elements.length === 2 && elements.every(el => isElementLine(el))) {
                // Line-based angle creation
                await this.performConstructionFromLines(elements, directionPoint, ctrl);
            }
        } catch (err: any) {
            this.resetState();
            console.error(err);
            throw err;
        }
    }

    /**
     * Create angle from three points (from CreateAngleByThreePointsTool)
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromPoints(
        points: (RenderVertex | any)[],
        directionPoint: RenderVertex | any,
        ctrl: GeoDocCtrl
    ) {
        // Ensure all are RenderVertex
        let pts = points.map(p => (isRenderVertex(p) ? p : vert(p)));
        const dirPt = isRenderVertex(directionPoint) ? directionPoint : vert(directionPoint);

        const [_p1, p2, _p3] = pts;

        // Calculate direction and swap if needed
        const { startPoint, endPoint } = this.getAngleDirection(pts, dirPt);
        pts = [startPoint, p2, endPoint];

        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        // Request names for points
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Angle Name',
                    originElement: pts,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];

        if (!inputPointNames.length) return;

        // Assign names and build construction points for unnamed points
        const constructionPoints: GeoElConstructionRequest[] = pts
            .map((p, i) => {
                if (!p.name) {
                    p.name = inputPointNames[i];
                    return buildPointConstruction(p.name, { x: p.coords[0], y: p.coords[1] });
                }
                return null;
            })
            .filter(Boolean) as GeoElConstructionRequest[];

        const angleName: string = pts.map(p => p.name).join('');
        const constructionAngle = this.buildAngleConstructionFromPoints(
            angleName,
            pts[0].name,
            pts[1].name,
            pts[2].name,
            pts[1].name.toUpperCase() < pts[0].name.toUpperCase() ? 1 : -1,
            pts[1].name.toUpperCase() < pts[2].name.toUpperCase() ? 1 : -1
        );

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Creating angle',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        ...constructionPoints.map(c => <ConstructionRequest>{ construction: c }),
                        { construction: constructionAngle },
                    ])
                );
                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
        this.resetState();
    }

    /**
     * Create angle from two lines (adapted from original CreateAngleTool)
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromLines(
        lines: RenderLine[],
        directionPoint: RenderVertex | any,
        ctrl: GeoDocCtrl
    ) {
        const [firstLine, secondLine] = lines;
        const dirPt = isRenderVertex(directionPoint) ? directionPoint : vert(directionPoint);

        const constructionPoints: GeoElConstructionRequest[] = [];

        // Get line information and ensure points exist
        const line1Result = await this.ensureLinePoints(ctrl, firstLine, 'Line 1');
        const line2Result = await this.ensureLinePoints(ctrl, secondLine, 'Line 2', line1Result?.names || []);

        if (!line1Result || !line2Result) {
            this.resetState();
            return;
        }

        // Calculate intersection point of the two lines
        const start1 = line1Result.start;
        const vec1 = firstLine.vector;
        const start2 = line2Result.start;
        const vec2 = secondLine.vector;
        const flattenLine1 = line(point(start1.coords[0], start1.coords[1]), vector([-vec1[1], vec1[0]]));
        const flattenLine2 = line(point(start2.coords[0], start2.coords[1]), vector([-vec2[1], vec2[0]]));
        const intersection = flattenLine1.intersect(flattenLine2)[0];
        if (!intersection) return;

        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;

        // Get the name of the intersection point
        const sharedNames = line1Result.names.filter((nm: any) => line2Result.names.includes(nm));
        let centerName: string = sharedNames.length ? sharedNames[0] : '';
        if (!centerName) {
            const vertexAtIntersection: RenderVertex = Object.assign(new RenderVertex(), {
                relIndex: -12,
                name: undefined,
                coords: [intersection.x, intersection.y],
                usable: true,
                valid: true,
            });

            const nameInputs = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Angle Point',
                        originElement: [vertexAtIntersection],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];
            if (!nameInputs.length) {
                this.resetState();
                return;
            }
            centerName = nameInputs[0];
        }

        // Calculate direction using the direction point
        const directionPos = [dirPt.coords[0], dirPt.coords[1]];
        const { lineStartName, lineStartType, lineEndName, lineEndType, startDirection, endDirection } =
            this.calculateLineBasedDirection(line1Result, line2Result, intersection, directionPos);

        // Create angle construction
        const angleConstruction = this.buildAngleConstructionFromLines(
            lineStartName,
            lineStartType,
            lineEndName,
            lineEndType,
            centerName,
            startDirection,
            endDirection
        );

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Creating angle',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        { construction: angleConstruction },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    /**
     * Helper method to get angle direction from three points (from CreateAngleByThreePointsTool)
     */
    private getAngleDirection(
        points: RenderVertex[],
        directionPoint: RenderVertex
    ): {
        startPoint: RenderVertex;
        endPoint: RenderVertex;
    } {
        const [p1, _p2, p3] = points;
        const fp = points.map(p => point(p.coords[0], p.coords[1]));
        const fp4 = point(directionPoint.coords[0], directionPoint.coords[1]);
        const v1 = vector(fp[1], fp[0]);
        const v2 = vector(fp[1], fp[2]);
        const v4 = vector(fp[1], fp4);
        const angle12 = v1.angleTo(v2);
        const angle14 = v1.angleTo(v4);
        if ((angle12 > 0 && angle14 > 0 && angle14 < angle12) || (angle12 < 0 && angle14 < 0 && angle14 > angle12)) {
            return {
                startPoint: p1,
                endPoint: p3,
            };
        } else {
            return {
                startPoint: p3,
                endPoint: p1,
            };
        }
    }

    /**
     * Helper method to ensure line points exist and get their information
     */
    private async ensureLinePoints(
        ctrl: any,
        line: RenderLine,
        prefix: string,
        excludeNames: string[] = []
    ): Promise<{ names: string[]; start: RenderVertex; end: RenderVertex; lineName: string; lineType: string } | null> {
        let start: RenderVertex,
            end: RenderVertex,
            names: string[] = [];

        if (line.name) {
            start = ctrl.rendererCtrl.elementAt(line.startPointIdx) as RenderVertex;
            end = ctrl.rendererCtrl.elementAt(line.endPointIdx) as RenderVertex;
            names = [start.name, end.name];
        } else {
            // For lines without names, we need to create points
            start = { coords: [0, 0], name: '', relIndex: line.startPointIdx } as RenderVertex;
            end = { coords: [0, 0], name: '', relIndex: line.endPointIdx } as RenderVertex;

            const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
            const originPoints = [start, end];
            const nameInputs = await requestElementNames(ctrl, nt, [
                {
                    objName: prefix,
                    originElement: originPoints,
                    pickName: pickPointName,
                    namesToAvoid: excludeNames,
                },
            ]);

            if (!nameInputs.length) return null;
            names = nameInputs[0];

            for (let i = 0; i < 2; i++) {
                if (!originPoints[i].name) {
                    originPoints[i].name = names[i];
                }
            }
        }

        return {
            names,
            start,
            end,
            lineName: names.length ? `${names[0]}${names[1]}` : line.name,
            lineType: line.elType,
        };
    }

    /**
     * Calculate direction for line-based angle creation
     */
    private calculateLineBasedDirection(line1Result: any, line2Result: any, intersection: any, directionPos: number[]) {
        // Use vector-based direction logic similar to the original implementation
        const p1 = { coords: [line1Result.start.coords[0], line1Result.start.coords[1]] };
        const p2 = { coords: [intersection.x, intersection.y] };
        const p3 = { coords: [line2Result.end.coords[0], line2Result.end.coords[1]] };
        const directionPoint = { coords: directionPos };

        // Build vectors
        const fp = [p1, p2, p3].map(p => point(p.coords[0], p.coords[1]));
        const fp4 = point(directionPoint.coords[0], directionPoint.coords[1]);
        const v1 = vector(fp[1], fp[0]);
        const v2 = vector(fp[1], fp[2]);
        const v4 = vector(fp[1], fp4);
        const angle12 = v1.angleTo(v2);
        const angle14 = v1.angleTo(v4);

        let needSwap = false;
        if ((angle12 > 0 && angle14 > 0 && angle14 < angle12) || (angle12 < 0 && angle14 < 0 && angle14 > angle12)) {
            needSwap = false;
        } else {
            needSwap = true;
        }

        let lineStartName = line1Result.lineName;
        let lineStartType = line1Result.lineType;
        let lineEndName = line2Result.lineName;
        let lineEndType = line2Result.lineType;
        let startDirection = 1;
        let endDirection = 1;

        if (needSwap) {
            [lineStartName, lineEndName] = [lineEndName, lineStartName];
            [lineStartType, lineEndType] = [lineEndType, lineStartType];
            [startDirection, endDirection] = [endDirection, startDirection];
        }

        return { lineStartName, lineStartType, lineEndName, lineEndType, startDirection, endDirection };
    }

    /**
     * Build angle construction from points (FromThreePoints method)
     */
    private buildAngleConstructionFromPoints(
        name: string,
        firstPointName: string,
        secondPointName: string,
        threePointName: string,
        startLineDirection: number,
        endLineDirection: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Angle/AngleEC', 'Angle', 'FromThreePoints');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: firstPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: secondPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: threePointName,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: [startLineDirection, endLineDirection],
                    },
                },
            },
        ];
        return construction;
    }

    /**
     * Build angle construction from lines (FromLinesAndDirection method)
     */
    private buildAngleConstructionFromLines(
        lineStartName: string,
        lineStartType: string,
        lineEndName: string,
        lineEndType: string,
        anglePointName: string,
        directionStart: number,
        directionEnd: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Angle/AngleEC', 'Angle', 'FromLinesAndDirection');
        construction.name = anglePointName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineStart',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-LineEnd',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineEndName,
                    },
                },
                dataTypes: {
                    name: lineEndType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-DirectionOfLineStart',
                params: {
                    value: {
                        type: 'singleValue',
                        value: directionStart,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-DirectionOfLineEnd',
                params: {
                    value: {
                        type: 'singleValue',
                        value: directionEnd,
                    },
                },
            },
        ];

        return construction;
    }
}
