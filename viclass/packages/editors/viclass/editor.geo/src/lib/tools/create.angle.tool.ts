import Flatten from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    RenderAngle,
    RenderLine,
    RenderLineSegment,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pAngle, pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { repeat, then, vertex, ThenSelector, vert } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './tool.utils';
import point = Flatten.point;
import line = Flatten.line;
import vector = Flatten.vector;

// Type guard for RenderVertex
function isRenderVertex(obj: any): obj is RenderVertex {
    return obj && Array.isArray(obj.coords);
}

/**
 * Unified tool to create an angle from three points or two lines.
 * Supports both point-based and line-based angle creation using selector DSL.
 * <AUTHOR>
 */
export class CreateAngleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateAngleTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    renderAngle: RenderAngle | undefined;
    basePoints: RenderVertex[] = [];
    baseLines: RenderLine[] = [];
    lastVertex: any;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.renderAngle = undefined;
        this.basePoints = [];
        this.baseLines = [];
        super.resetState();
    }

    /**
     * Creates the selection logic for point-based angle creation (like the original CreateAngleByThreePointsTool).
     * This is simpler and more reliable.
     */
    private createSelLogic() {
        // Use the same approach as CreateAngleByThreePointsTool - select 3 points then 1 direction point
        this.selLogic = then(
            [
                repeat(
                    vertex({
                        previewQueue: this.pQ,
                        cursor: this.pointerHandler.cursor,
                        refinedFilter: (el: RenderVertex) => !this.basePoints.includes(el),
                    }),
                    {
                        count: 3,
                        onPartialSelection: newSel => {
                            const v = isRenderVertex(newSel) ? newSel : vert(newSel);
                            this.basePoints.push(v);
                            return true;
                        },
                    }
                ),
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    refinedFilter: (el: RenderVertex) => !this.basePoints.includes(el),
                }),
            ],
            {
                onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                    const [points, directionPoint] = selector.selected;
                    this.performConstruction(points, directionPoint, doc);
                },
            }
        );
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }
        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    private doTrySelection(event: any, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected) {
            this.handlePreview(selected, ctrl);
        }
        this.pQ.flush(ctrl);
    }

    private handlePreview(selected: any, ctrl: GeoDocCtrl) {
        if (selected.length === 2) {
            // We have 3 points + direction point - preview the angle
            const [points, directionPoint] = selected;
            if (Array.isArray(points) && points.length === 3) {
                this.previewAngleFromPoints(points, directionPoint, ctrl);
            }
        } else if (selected.length === 1 && Array.isArray(selected[0])) {
            // Partial selection - show preview lines between points
            const points = selected[0];
            if (points.length === 3) {
                // Three points - preview the two line segments
                const [p1, p2, p3] = points.map(el => (isRenderVertex(el) ? el : vert(el)));
                this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p1, p2));
                this.pQ.add(pLine(ctrl, -23, RenderLineSegment, p2, p3));
            } else if (points.length === 2) {
                // Two points - preview line segment
                const [p1, p2] = points.map(el => (isRenderVertex(el) ? el : vert(el)));
                this.pQ.add(pLine(ctrl, -21, RenderLineSegment, p1, p2));
            }
        }
    }

    private previewAngleFromPoints(points: any[], directionPoint: any, ctrl: GeoDocCtrl) {
        const [p1, p2, p3] = points.map(p => (isRenderVertex(p) ? p : vert(p))) as RenderVertex[];
        if (!isRenderVertex(directionPoint)) return;
        const p4 = directionPoint as RenderVertex;

        this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p1, p2));
        this.pQ.add(pLine(ctrl, -23, RenderLineSegment, p2, p3));

        // Determine angle direction
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]);
        const fp3 = point(p3.coords[0], p3.coords[1]);
        const fp4 = point(p4.coords[0], p4.coords[1]);
        const v1 = vector(fp2, fp1);
        const v2 = vector(fp2, fp3);
        const v4 = vector(fp2, fp4);
        const angle12 = v1.angleTo(v2);
        const angle14 = v1.angleTo(v4);

        if ((angle12 > 0 && angle14 > 0 && angle14 < angle12) || (angle12 < 0 && angle14 < 0 && angle14 > angle12)) {
            const line1 = pLine(ctrl, -25, RenderLineSegment, p2, p1);
            const line2 = pLine(ctrl, -26, RenderLineSegment, p2, p3);
            this.pQ.add(pAngle(ctrl, -24, line1, line2));
        } else {
            const line1 = pLine(ctrl, -25, RenderLineSegment, p2, p3);
            const line2 = pLine(ctrl, -26, RenderLineSegment, p2, p1);
            this.pQ.add(pAngle(ctrl, -24, line1, line2));
        }
    }

    /**
     * Main construction method for point-based angle creation
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(elements: any, directionPoint: any, ctrl: GeoDocCtrl) {
        try {
            if (
                Array.isArray(elements) &&
                elements.length === 3 &&
                elements.every(el => isRenderVertex(el) || vert(el))
            ) {
                // Point-based angle creation
                await this.performConstructionFromPoints(elements, directionPoint, ctrl);
            }
        } catch (err: any) {
            this.resetState();
            console.error(err);
            throw err;
        }
    }

    /**
     * Create angle from three points (from CreateAngleByThreePointsTool)
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstructionFromPoints(
        points: (RenderVertex | any)[],
        directionPoint: RenderVertex | any,
        ctrl: GeoDocCtrl
    ) {
        // Ensure all are RenderVertex
        let pts = points.map(p => (isRenderVertex(p) ? p : vert(p)));
        const dirPt = isRenderVertex(directionPoint) ? directionPoint : vert(directionPoint);

        const [_p1, p2, _p3] = pts;

        // Calculate direction and swap if needed
        const { startPoint, endPoint } = this.getAngleDirection(pts, dirPt);
        pts = [startPoint, p2, endPoint];

        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        // Request names for points
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Angle Name',
                    originElement: pts,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];

        if (!inputPointNames.length) return;

        // Assign names and build construction points for unnamed points
        const constructionPoints: GeoElConstructionRequest[] = pts
            .map((p, i) => {
                if (!p.name) {
                    p.name = inputPointNames[i];
                    return buildPointConstruction(p.name, { x: p.coords[0], y: p.coords[1] });
                }
                return null;
            })
            .filter(Boolean) as GeoElConstructionRequest[];

        const angleName: string = pts.map(p => p.name).join('');
        const constructionAngle = this.buildAngleConstructionFromPoints(
            angleName,
            pts[0].name,
            pts[1].name,
            pts[2].name,
            pts[1].name.toUpperCase() < pts[0].name.toUpperCase() ? 1 : -1,
            pts[1].name.toUpperCase() < pts[2].name.toUpperCase() ? 1 : -1
        );

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Creating angle',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        ...constructionPoints.map(c => <ConstructionRequest>{ construction: c }),
                        { construction: constructionAngle },
                    ])
                );
                await syncRenderCommands(constructResponse.render, ctrl);
                addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
        this.resetState();
    }

    /**
     * Helper method to get angle direction from three points (from CreateAngleByThreePointsTool)
     */
    private getAngleDirection(
        points: RenderVertex[],
        directionPoint: RenderVertex
    ): {
        startPoint: RenderVertex;
        endPoint: RenderVertex;
    } {
        const [p1, _p2, p3] = points;
        const fp = points.map(p => point(p.coords[0], p.coords[1]));
        const fp4 = point(directionPoint.coords[0], directionPoint.coords[1]);
        const v1 = vector(fp[1], fp[0]);
        const v2 = vector(fp[1], fp[2]);
        const v4 = vector(fp[1], fp4);
        const angle12 = v1.angleTo(v2);
        const angle14 = v1.angleTo(v4);
        if ((angle12 > 0 && angle14 > 0 && angle14 < angle12) || (angle12 < 0 && angle14 < 0 && angle14 > angle12)) {
            return {
                startPoint: p1,
                endPoint: p3,
            };
        } else {
            return {
                startPoint: p3,
                endPoint: p1,
            };
        }
    }

    /**
     * Build angle construction from points (FromThreePoints method)
     */
    private buildAngleConstructionFromPoints(
        name: string,
        firstPointName: string,
        secondPointName: string,
        threePointName: string,
        startLineDirection: number,
        endLineDirection: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Angle/AngleEC', 'Angle', 'FromThreePoints');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: firstPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: secondPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: threePointName,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: [startLineDirection, endLineDirection],
                    },
                },
            },
        ];
        return construction;
    }
}
