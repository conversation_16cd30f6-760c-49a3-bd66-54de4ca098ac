﻿import {
    ErrorH<PERSON>lerDecorator,
    KeyboardHandlingItem,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderAngle,
    RenderLine,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeometryTool } from './geo.tool';
import { handleIfPointerNotInError } from './tool.utils';

/**
 *
 * <AUTHOR>
 */
export class CreateBisectorLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateBisectorLineTool';

    private pointEnd: RenderVertex;
    private intersectLine: RenderLine;
    private angle: RenderAngle;
    private bisectorVector: number[];
    private isPointerDown = false;
    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderAngle' && !this.angle;
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },

            // move point/preview
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['nokey'] },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn },

            // with shift key
            { event: 'pointerdown', keys: ['shift'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', keys: ['shift'], button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['shift'] }
        );
        this.registerKeyboardHandling(
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keyup';
            })(['shift']),
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keydown';
            })(['shift'])
        );
    }

    override resetState() {
        this.pointEnd = undefined;
        this.intersectLine = undefined;
        this.angle = undefined;
        this.bisectorVector = undefined;
        this.isPointerDown = false;
        super.resetState();
    }

    private calculateBiSectorVectorOfAngle(a, b) {
        // Calculate the dot product of a and b
        const dotProduct = a[0] * b[0] + a[1] * b[1];

        // Magnitude of vector a
        const magnitudeA = Math.sqrt(a[0] * a[0] + a[1] * a[1]);

        // Magnitude of vector b
        const magnitudeB = Math.sqrt(b[0] * b[0] + b[1] * b[1]);

        // Calculate the cosine of the angle between a and b
        const cosTheta = dotProduct / (magnitudeA * magnitudeB);

        // Calculate the sine of the angle between a and b
        const sinTheta = Math.sqrt(1 - cosTheta * cosTheta);

        // Calculate the x and y components of the angle bisector vector
        const cX = (magnitudeA * b[1] - magnitudeB * a[1]) / (2 * sinTheta);
        const cY = (magnitudeB * a[0] - magnitudeA * b[0]) / (2 * sinTheta);

        return [cX, cY];
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;
    }

    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return;
        this.isPointerDown = false;

        if (!this.angle) {
            await this.onSelectClick(event);
        } else {
            await this.onPreviewClick(event);
        }
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (!this.angle || !this.bisectorVector) return;
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        // const { ctrl, pos, hitEl } = this.posAndCtrl(event);
        // if (!this.angle) return;
        // let mpg = [pos.x, pos.y];
        // const v = this.bisectorVector;
        // const pS = ctrl.rendererCtrl.elementAt(this.angle.anglePointIdx) as RenderVertex;
        // const p = pS.coords;
        // if (hitEl) {
        //     if (isElementLine(hitEl)) {
        //         const e = hitEl as RenderLine;
        //         const pS = ctrl.rendererCtrl.elementAt(e.startPointIdx) as RenderVertex;
        //         const vS = e.vector;
        //         const pPer = ctrl.rendererCtrl.elementAt(this.angle.anglePointIdx) as RenderVertex;
        //         const vPer = this.bisectorVector;
        //         const lineS = line(point(pS.coords[0], pS.coords[1]), vector([-vS[1], vS[0]]));
        //         const linePer = line(point(pPer.coords[0], pPer.coords[1]), vector([-vPer[1], vPer[0]]));
        //         const intersectPoint = lineS.intersect(linePer)[0];
        //         if (intersectPoint) {
        //             mpg = [intersectPoint.x, intersectPoint.y];
        //             const pointPreview: RenderVertex = {
        //                 unselectable: true,
        //                 relIndex: -13,
        //                 type: 'RenderVertex',
        //                 elType: 'Point',
        //                 renderProp: buildPreviewVertexRenderProp(),
        //                 name: '',
        //                 coords: mpg,
        //                 usable: true,
        //                 valid: true,
        //             };
        //             syncPreviewCommands(pointPreview, ctrl);
        //             return;
        //         }
        //     }
        // }
        // const [x, y] = projectPointOntoLine(mpg, p, v);
        // const sameDirection = vector(v[0], v[1]).angleTo(vector(x - p[0], y - p[1])) * (180 / Math.PI) == 180 ? -1 : 1;
        // if (isSamePoint(mpg, [x, y], ctrl) && sameDirection == 1) {
        //     const point: PreviewVertex = {
        //         unselectable: true,
        //         relIndex: -13,
        //         type: 'RenderVertex',
        //         elType: 'Point',
        //         renderProp: buildPreviewVertexRenderProp(),
        //         name: '',
        //         coords: [x, y],
        //         usable: true,
        //         valid: true,
        //     };
        //     syncPreviewCommands(point, ctrl);
        // } else {
        //     const p: PreviewVertex = {
        //         unselectable: true,
        //         relIndex: -13,
        //         type: 'RenderVertex',
        //         elType: 'Point',
        //         renderProp: buildPreviewVertexRenderProp(),
        //         name: '',
        //         coords: [],
        //         usable: true,
        //         valid: true,
        //     };
        //     syncPreviewCommands(p, ctrl);
        // }
    }

    private async onPointerClick(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.angle) {
            await this.onSelectClick(event);
        } else {
            await this.onPreviewClick(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onSelectClick(event: GeoPointerEvent) {
        // const { ctrl, hitCtx, hitEl } = this.posAndCtrl(event);
        // if (hitEl) {
        //     if (hitEl.type == 'RenderAngle') {
        //         this.angle = hitEl as RenderAngle;
        //         this.bisectorVector = this.calculateBiSectorVectorOfAngle(this.angle.vectorStart, this.angle.vectorEnd);
        //     }
        //     ctrl.editor.selectElement(hitCtx, true);
        // }
        // if (this.bisectorVector && this.angle) {
        //     const v: number[] = this.bisectorVector;
        //     const pS = ctrl.rendererCtrl.elementAt(this.angle.anglePointIdx) as RenderVertex;
        //     const p = pS.coords;
        //     const linePreview: PreviewLine = {
        //         relIndex: -20,
        //         type: 'RenderRay',
        //         elType: 'Ray',
        //         name: '',
        //         renderProp: buildPreviewLineSegmentRenderProp(),
        //         startPoint: p,
        //         endPoint: null,
        //         vector: [...v],
        //         usable: true,
        //         valid: true,
        //     };
        //     syncPreviewCommands(linePreview, ctrl);
        //     this.editor.filterElementFunc = (el: GeoRenderElement) => isElementLine(el);
        // }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPreviewClick(event: GeoPointerEvent) {
        // const { ctrl, hitEl, pos } = this.posAndCtrl(event);
        // const mpg = [pos.x, pos.y];
        // const v = this.bisectorVector;
        // const pS = ctrl.rendererCtrl.elementAt(this.angle.anglePointIdx) as RenderVertex;
        // const p = pS.coords;
        // if (hitEl) {
        //     if (isElementLine(hitEl)) {
        //         this.intersectLine = hitEl as RenderLine;
        //     } else if (hitEl.type == 'RenderVertex') {
        //         this.pointEnd = hitEl as RenderVertex;
        //     }
        // } else if (!this.pointEnd && !this.intersectLine) {
        //     const [x, y] = projectPointOntoLine(mpg, p, v);
        //     const sameDirection =
        //         vector(v[0], v[1]).angleTo(vector(x - p[0], y - p[1])) * (180 / Math.PI) == 180 ? -1 : 1;
        //     if (isSamePoint(mpg, [x, y], ctrl) && sameDirection == 1) {
        //         this.pointEnd = {
        //             unselectable: false,
        //             relIndex: -13,
        //             type: 'RenderVertex',
        //             elType: 'Point',
        //             renderProp: buildPreviewVertexRenderProp(),
        //             name: '',
        //             coords: [x, y],
        //             usable: true,
        //             valid: true,
        //         };
        //     }
        // }
        // const angleName = this.angle.name;
        // let constructionAngle = undefined;
        // if (!this.pointEnd && !this.intersectLine) {
        //     constructionAngle = this.buildBisectorConstruction(angleName);
        // } else if (this.pointEnd && !this.intersectLine) {
        //     const inputPointNames = (
        //        await requestElementNames(ctrl, nt, [
        //            {
        //               objName: 'Bisector Point',
        //              originElement: [this.pointEnd],
        //             pickName: pickPointName,
        //              namesToAvoid: [],
        //          },
        //      ])
        //  )[0];
        //     if (!inputPointNames.length) {
        //         this.resetState();
        //         return;
        //     }
        //     const lVector = this.bisectorVector;
        //     const pointStart = pS.coords;
        //     const pointEnd = this.pointEnd.coords;
        //     const uVector = calculateUnitVector(lVector);
        //     const k = calculateScalingFactor(uVector, pointStart, pointEnd);
        //     constructionAngle = this.buildBisectorSegmentConstruction(inputPointNames.join(''), angleName, k);
        // } else if (!this.pointEnd && this.intersectLine) {
        //     const intersectLineName = this.intersectLine.name;
        //     const intersectLineType = this.intersectLine.elType;
        //     constructionAngle = this.buildBisectorSegmentAndIntersectionLineConstruction(
        //         angleName,
        //         intersectLineName,
        //         intersectLineType
        //     );
        // }
        // await ctrl.editor.awarenessFeature.useAwareness(
        //     ctrl.viewport.id,
        //     'Đang tạo đường phân giác',
        //     buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        //     async () => {
        //         const constructResponse = await constructExec(() =>
        //             this.editor.geoGateway.construct(ctrl.state.globalId, [
        //                 {
        //                     construction: constructionAngle,
        //                 },
        //             ])
        //         );
        //         await syncRenderCommands(constructResponse.render, ctrl);
        //         await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
        //         this.resetState();
        //     }
        // );
    }

    private buildBisectorConstruction(angleName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngle');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentConstruction(name: string, angleName: string, k: number): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngleSegment');
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentAndIntersectionLineConstruction(
        angleName: string,
        intersectionLineName: string,
        intersectionLineType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/BisectorOfAngleEC',
            'LineVi',
            'BisectorAngleSegmentWithIntersectionLine'
        );

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
