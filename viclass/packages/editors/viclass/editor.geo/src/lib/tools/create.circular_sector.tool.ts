import { KeyboardHandlingItem, UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, GeoRenderElement, RenderLineSegment, RenderVertex } from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { or, repeat, stroke, then, ThenSelector, vertex, VertexSelector } from '../selectors';
import { getFocusDocCtrl } from '../tools/tool.utils';
import { GeometryTool } from './geo.tool';
import { handleIfPointerNotInError } from './tool.utils';

export class CreateSectorTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateSectorTool';

    protected basePoints: GeoRenderElement[] = [];
    pQ = new PreviewQueue();
    lastVertex: VertexSelector;
    private clockwise: boolean = false;

    declare selLogic?: ThenSelector;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.registerKeyboardHandling(
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keyup';
            })(['shift']),
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keydown';
            })(['shift'])
        );
    }

    override resetState() {
        this.selLogic.reset();
        this.basePoints = [];
        super.resetState();
    }

    excludeSelection(el: GeoRenderElement) {
        return !this.basePoints.includes(el);
    }

    protected createSelLogic() {
        this.lastVertex = vertex({
            // then select a vertex
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: this.excludeSelection.bind(this),
        });
        this.selLogic = then(
            [
                or(
                    // either
                    [
                        repeat<RenderVertex>( // select two point repeatedly
                            vertex({
                                previewQueue: this.pQ,
                                cursor: this.pointerHandler.cursor,
                                refinedFilter: this.excludeSelection.bind(this), // do not reselect anything has been selected
                                onComplete: selector => this.basePoints.push(selector.selected),
                            }),
                            {
                                count: 2,
                            }
                        ),
                        stroke({
                            // or select a line segment
                            selectableStrokeTypes: ['RenderLineSegment'],
                            cursor: this.pointerHandler.cursor,
                            onComplete: (selector, doc) => {
                                const selected = selector.selected as RenderLineSegment;
                                const point1 = doc.rendererCtrl.elementAt(selected.startPointIdx);
                                const point2 = doc.rendererCtrl.elementAt(selected.endPointIdx);
                                if (!point1 || !point2) throw new Error('Non line segment selected');
                                this.basePoints.push(point1, point2);
                            },
                        }),
                    ],
                    { flatten: true }
                ),
                this.lastVertex,
            ],
            {
                onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
            }
        );
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        event = super.handleKeyboardEvent(event);
        if (event.nativeEvent.key == 'Shift') {
            this.clockwise = !this.clockwise;
        }
        return event;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl));

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected) {
            if (selected.length == 2) {
                // has all data, preview triangle
                const bp1 = this.basePoints[0];
                const bp2 = this.basePoints[1];
                this.pQ.add(pLine(ctrl, -21, RenderLineSegment, bp1 as RenderVertex, selected[1] as RenderVertex));
                this.pQ.add(pLine(ctrl, -22, RenderLineSegment, bp2 as RenderVertex, selected[1] as RenderVertex));
            } else if (selected.length == 1 && Array.isArray(selected[0]) && selected[0].length == 2) {
                // preview the base
                this.pQ.add(
                    pLine(ctrl, -20, RenderLineSegment, selected[0][0] as RenderVertex, selected[0][1] as RenderVertex)
                );
            }
        }
        this.pQ.flush(ctrl);
    }

    performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {
        this.resetState();
    }

    // @ErrorHandlerDecorator([geoDefaultHandlerFn])
    // private async onPointerDown(event: GeoPointerEvent) {
    //     if (!this.shouldHandleClick(event)) return;
    //     event.continue = false;
    //     event.nativeEvent.preventDefault();

    //     if (this.isPointerDown) return; // don't handle if pointer down is already set
    //     this.isPointerDown = true;

    //     this.clickCount++;
    //     switch (this.clickCount) {
    //         case 1:
    //             this.onFirstClick(event);
    //             break;
    //         case 2:
    //             this.onSecondClick(event);
    //             break;
    //         default:
    //             this.onFinalClick(event);
    //             break;
    //     }
    // }

    // @ErrorHandlerDecorator([geoDefaultHandlerFn])
    // private async onPointerUp(event: GeoPointerEvent) {
    //     if (!this.shouldHandleClick(event) || !this.points.length) return;
    //     event.continue = false;
    //     event.nativeEvent.preventDefault();

    //     if (!this.isPointerDown) return;
    //     this.isPointerDown = false;
    //     this.potentialSelectionDelegator.clearPotential();

    //     // If we just added the third point, submit construction
    //     if (this.points.length == 3 && this.clickCount > 2) {
    //         this.submitConstruction(event);
    //     }
    // }

    // private onPointerMove(event: GeoPointerEvent) {
    //     this.lastPointerMove = event;
    //     if (!this.points.length) return;
    //     this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
    //     event.continue = false;
    //     event.nativeEvent.preventDefault();
    // }

    // private pointerMoveCallback(event: GeoPointerEvent) {
    //     handleIfPointerNotInError(this, () => {
    //         this.processPointerMove(event);
    //     });
    // }

    // @ErrorHandlerDecorator([geoDefaultHandlerFn])
    // private processPointerMove(event: GeoPointerEvent) {
    //     if (!this.points.length) return;

    //     event.continue = false;
    //     event.nativeEvent.preventDefault();

    //     switch (this.clickCount) {
    //         case 1:
    //             if (this.isPointerDown && !this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event))
    //                 this.onFirstClick(event);
    //             return; // only update sector preview after 2nd click
    //         case 2:
    //             if (this.isPointerDown && !this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event))
    //                 this.onSecondClick(event);
    //             break;
    //         default:
    //             this.onFinalClick(event);
    //             break;
    //     }

    //     // update sector preview
    //     const { ctrl, pos, hitCtx, hitEl } = this.posAndCtrl(event);
    //     let vE = [pos.x, pos.y, 0.0];

    //     if (hitEl?.type == 'RenderVertex') {
    //         vE = (hitEl as RenderVertex).coords;
    //     }
    //     const vC = this.points[0].coords.concat(0);
    //     const vS = this.points[1].coords.concat(0);

    //     const current = this.points[2]?.coords || vE;
    //     this.previewSector(ctrl, vC, vS, current);
    // }

    // private previewLine(ctrl, startPoint, endPoint) {
    // const previewLine: PreviewLine = {
    //     relIndex: -21,
    //     name: '',
    //     type: 'RenderLineSegment',
    //     elType: 'LineSegment',
    //     startPoint: startPoint,
    //     endPoint: endPoint,
    //     renderProp: buildPreviewLineSegmentRenderProp(),
    //     usable: true,
    //     valid: true,
    // };
    // syncPreviewCommands(previewLine, ctrl);
    // }

    // private previewSector(ctrl, centerPoint, startPoint, endPoint) {
    //     const pS = point(startPoint[0], startPoint[1]);
    //     const pE = point(endPoint[0], endPoint[1]);
    //     const pC = point(centerPoint[0], centerPoint[1]);
    //     const c = circle(pC, this.circle.radius);
    //     const l1 = line(pC, pS);
    //     const l2 = line(pC, pE);
    //     const i1 = c.intersect(l1);
    //     const i2 = c.intersect(l2);

    //     if (i1.length < 2 || i2.length < 2) return;

    //     let vS = [...startPoint];
    //     let vE = [...endPoint];

    //     if (pS.distanceTo(i1[0])[0] < pS.distanceTo(i1[1])[0]) {
    //         vS = [i1[0].x, i1[0].y, 0];
    //     } else {
    //         vS = [i1[1].x, i1[1].y, 0];
    //     }

    //     if (pE.distanceTo(i2[0])[0] < pE.distanceTo(i2[1])[0]) {
    //         vE = [i2[0].x, i2[0].y, 0];
    //     } else {
    //         vE = [i2[1].x, i2[1].y, 0];
    //     }

    //     if (this.clockwise) {
    //         const tmp = vS;
    //         vS = vE;
    //         vE = tmp;
    //     }

    //     // this.shape = {
    //     //     relIndex: -22,
    //     //     name: '',
    //     //     type: 'RenderSectorShape',
    //     //     elType: 'CircularSector',
    //     //     renderProp: buildPreviewSectorShapeRenderProp(),
    //     //     usable: true,
    //     //     valid: true,
    //     //     centerPoint: centerPoint,
    //     //     startPoint: vS,
    //     //     endPoint: vE,
    //     // };

    //     // syncPreviewCommands(this.shape, ctrl);
    // }

    private buildPointOnCircleConstruction(name: string, circleName: string, angle: number): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Point/PointOnCircleEC', 'Point', 'OnCircleWithRadian');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aCircle',
                optional: false,
                tplStrLangId: 'tpl-OnCircle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: circleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-RangeRadian',
                params: {
                    value: {
                        type: 'singleValue',
                        value: angle,
                    },
                },
            },
        ];

        return construction;
    }

    private buildCSOnCircleConstruction(
        name: string,
        cName: string,
        sName: string,
        eName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'CircularSector/CircularSectorEC',
            'CircularSector',
            'PointsOnCircle'
        );
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aCircle',
                optional: false,
                tplStrLangId: 'tpl-OnCircle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: cName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-StartEndPoint',
                params: {
                    name: {
                        type: 'array',
                        values: [sName, eName],
                    },
                },
            },
        ];

        return construction;
    }

    private buildCSWithCenterAndStartEndPointConstruction(
        cName: string,
        sName: string,
        eName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'CircularSector/CircularSectorEC',
            'CircularSector',
            'WithCenterAndStartEndPoint'
        );
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-CenterCircle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: cName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-StartEndPoint',
                params: {
                    name: {
                        type: 'array',
                        values: [sName, eName],
                    },
                },
            },
        ];

        return construction;
    }

    private buildCSWithCenterPointAndRadianConstruction(
        name: string,
        cName: string,
        sName: string,
        angleRadian: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'CircularSector/CircularSectorEC',
            'CircularSector',
            'WithCenterAndStartPointAngleRadian'
        );
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-CenterCircle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: cName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Point',
                params: {
                    name: {
                        type: 'singleValue',
                        value: sName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-AngleRadian',
                params: {
                    value: {
                        type: 'singleValue',
                        value: angleRadian,
                    },
                },
            },
        ];

        return construction;
    }

    // private async onFirstClick(event: GeoPointerEvent) {
    //     const { ctrl, pos, hitCtx, hitEl } = this.posAndCtrl(event);
    //     let vC = [pos.x, pos.y, 0];
    //     let vS = [pos.x, pos.y, 0];

    //     if (hitEl) {
    //         ctrl.editor.selectElement(hitCtx, true);
    //         if (hitEl.type == 'RenderVertex') {
    //             const p = hitEl as RenderVertex;
    //             this.points[0] = p;
    //             vC = p.coords;
    //             vS = p.coords;

    //             this.editor.filterElementFunc = el =>
    //                 (el.type == 'RenderVertex' || el.type == 'RenderCircle' || el.type == 'RenderSector') &&
    //                 (el as RenderSector).centerPointIdx == p.relIndex;
    //         } else if (hitEl.type == 'RenderSector' || hitEl.type == 'RenderCircle') {
    //             const c = hitEl as RenderCircleShape;
    //             const center = ctrl.rendererCtrl.elementAt(c.centerPointIdx) as RenderVertex;
    //             this.circle = c;
    //             this.points[0] = center;
    //             vC = center.coords;
    //             const p: RenderVertex = {
    //                 relIndex: -10,
    //                 type: 'RenderVertex',
    //                 elType: 'Point',
    //                 renderProp: buildPreviewVertexRenderProp(),
    //                 name: undefined,
    //                 coords: [pos.x, pos.y],
    //                 usable: true,
    //                 valid: true,
    //             };
    //             vS = p.coords;
    //             this.points[1] = p;

    //             this.editor.filterElementFunc = el => el.type == 'RenderVertex';
    //         } else return;
    //     } else {
    //         const p: RenderVertex = {
    //             relIndex: -10,
    //             type: 'RenderVertex',
    //             elType: 'Point',
    //             renderProp: buildPreviewVertexRenderProp(),
    //             name: undefined,
    //             coords: [pos.x, pos.y],
    //             usable: true,
    //             valid: true,
    //         };
    //         this.points[0] = p;
    //     }

    //     this.previewLine(ctrl, vC, vS);
    // }

    // private async onSecondClick(event: GeoPointerEvent) {
    //     const { ctrl, pos, hitCtx, hitEl } = this.posAndCtrl(event);
    //     const vC = [this.points[0].coords[0], this.points[0].coords[1], 0];
    //     let vS = [pos.x, pos.y, 0.0];

    //     if (hitEl) {
    //         ctrl.editor.selectElement(hitCtx, true);
    //         if (hitEl.type == 'RenderVertex') {
    //             this.points[1] = hitEl as RenderVertex;
    //             vS = (hitEl as RenderVertex).coords;
    //             if (!this.circle || !this.circle.vertexRelIdxes.includes(hitEl.relIndex)) {
    //                 // this.newCircle = {
    //                 //     relIndex: -20,
    //                 //     type: 'RenderCircleShape',
    //                 //     elType: 'Circle',
    //                 //     centerPointIdx: this.points[0].relIndex,
    //                 //     radius: distance2Point(vC, vS),
    //                 //     renderProp: buildPreviewCircleShapeRenderProp(),
    //                 //     name: undefined,
    //                 //     usable: true,
    //                 //     valid: true,
    //                 // };
    //                 // this.circle = this.newCircle;
    //             }
    //         } else if (hitEl.type == 'RenderCircle' || hitEl.type == 'RenderSector') {
    //             this.circle = hitEl as RenderCircleShape;
    //             const p: RenderVertex = {
    //                 relIndex: -11,
    //                 type: 'RenderVertex',
    //                 elType: 'Point',
    //                 renderProp: buildPreviewVertexRenderProp(),
    //                 name: undefined,
    //                 coords: [pos.x, pos.y],
    //                 usable: true,
    //                 valid: true,
    //             };
    //             this.points[1] = p;
    //         } else return;
    //     } else {
    //         const p: RenderVertex = {
    //             relIndex: -11,
    //             type: 'RenderVertex',
    //             elType: 'Point',
    //             renderProp: buildPreviewVertexRenderProp(),
    //             name: undefined,
    //             coords: [pos.x, pos.y],
    //             usable: true,
    //             valid: true,
    //         };
    //         this.points[1] = p;
    //         // this.newCircle = {
    //         //     relIndex: -20,
    //         //     type: 'RenderCircleShape',
    //         //     elType: 'Circle',
    //         //     centerPointIdx: this.points[0].relIndex,
    //         //     radius: distance2Point(vC, vS),
    //         //     renderProp: buildPreviewCircleShapeRenderProp(),
    //         //     name: undefined,
    //         //     usable: true,
    //         //     valid: true,
    //         // };
    //         this.circle = this.newCircle;
    //     }

    //     this.previewLine(ctrl, vC, vS);

    //     this.editor.filterElementFunc = el => el.type == 'RenderVertex';
    // }

    // private async onFinalClick(event: GeoPointerEvent) {
    //     const { ctrl, pos, hitEl, docGlobalId } = this.posAndCtrl(event);
    //     let vE = [pos.x, pos.y];

    //     if (hitEl) {
    //         const el = hitEl as RenderVertex;
    //         this.points[2] = el;
    //         vE = el.coords;
    //     } else {
    //         const p: RenderVertex = {
    //             relIndex: undefined,
    //             type: 'RenderVertex',
    //             elType: 'Point',
    //             renderProp: undefined,
    //             name: undefined,
    //             coords: vE,
    //             usable: true,
    //             valid: true,
    //         };
    //         this.points[2] = p;
    //     }
    // }

    // private async submitConstruction(event: GeoPointerEvent) {
    // const { ctrl, pos, hitEl, docGlobalId } = this.posAndCtrl(event);
    // let vE = [pos.x, pos.y];
    // const vC = this.points[0].coords;
    // const vS = this.points[1].coords;
    // const pS = point(vS[0], vS[1]);
    // let pE = point(vE[0], vE[1]);
    // const pC = point(vC[0], vC[1]);
    // const c = circle(pC, this.circle.radius);
    // const l = line(pC, pE);
    // const i = c.intersect(l);
    // if (i.length < 2) throw 'invalid circular sector';
    // if (pE.distanceTo(i[0])[0] < pE.distanceTo(i[1])[0]) {
    //     vE = [i[0].x, i[0].y, 0];
    // } else {
    //     vE = [i[1].x, i[1].y, 0];
    // }
    // pE = point(vE[0], vE[1]);
    // const vec0 = vector(pC, point(pC.x + 10, pC.y));
    // const vecS = vector(pC, pS);
    // const vecE = vector(pC, pE);
    // // submit construction
    // const constructionPoints: GeoElConstructionRequest[] = [];
    // if (this.points.length == 3 && !this.points.find(p => !p.name)) {
    //     const constructionCS = this.buildCSWithCenterAndStartEndPointConstruction(
    //         this.points[0].name,
    //         this.points[1].name,
    //         this.points[2].name
    //     );
    //     this.resetState();
    //     await ctrl.editor.awarenessFeature.useAwareness(
    //         ctrl.viewport.id,
    //         'Đang tạo cung tròn',
    //         buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
    //         async () => {
    //             const constructResponse: ApplyConstructionResponse = await constructExec(() =>
    //                 this.editor.geoGateway.construct(docGlobalId, [
    //                     {
    //                         construction: constructionCS,
    //                     },
    //                 ])
    //             );
    //             await syncRenderCommands(constructResponse.render, ctrl);
    //             await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
    //         }
    //     );
    // } else if (this.newCircle) {
    //     const inputEleNames = await requestElementNames(ctrl, nt, [
    //         {
    //             objName: 'Cung',
    //             originElement: this.points,
    //             pickName: pickPointName,
    //             namesToAvoid: [],
    //         },
    //         {
    //             objName: 'Đường Cung',
    //             originElement: [this.shape],
    //             pickName: pickShapeName,
    //             namesToAvoid: [],
    //         },
    //     ]);
    //     const inputPointNames = inputEleNames[0];
    //     const inputShapeNames = inputEleNames[1];
    //     if (!inputPointNames.length) {
    //         this.resetState();
    //         return;
    //     }
    //     for (let i = 0; i < this.points.length - 1; i++) {
    //         const p = this.points[i];
    //         if (!p.name) {
    //             p.name = inputPointNames[i];
    //             const constructionPoint = buildPointConstruction(p.name, {
    //                 x: p.coords[0],
    //                 y: p.coords[1],
    //             });
    //             constructionPoints.push(constructionPoint);
    //         }
    //     }
    //     const pNameS: string = this.points[1].name;
    //     let angle = vecS.angleTo(vecE);
    //     if (this.clockwise) {
    //         angle = -vecE.angleTo(vecS);
    //     }
    //     const constructionCS = this.buildCSWithCenterPointAndRadianConstruction(
    //         inputShapeNames[0],
    //         this.points[0].name,
    //         pNameS,
    //         angle
    //     );
    //     this.resetState();
    //     await ctrl.editor.awarenessFeature.useAwareness(
    //         ctrl.viewport.id,
    //         'Đang tạo cung tròn',
    //         buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
    //         async () => {
    //             const constructResponse: ApplyConstructionResponse = await constructExec(() =>
    //                 this.editor.geoGateway.construct(docGlobalId, [
    //                     ...constructionPoints.map(
    //                         c =>
    //                             <ConstructionRequest>{
    //                                 construction: c,
    //                             }
    //                     ),
    //                     {
    //                         construction: constructionCS,
    //                     },
    //                 ])
    //             );
    //             await syncRenderCommands(constructResponse.render, ctrl);
    //             await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
    //         }
    //     );
    // } else {
    //     const angles = [vec0.angleTo(vecS), vec0.angleTo(vecE)];
    //     const inputPointNames = (
    //         await requestElementNames(ctrl, nt, [
    //             {
    //                 objName: 'Cung',
    //                 originElement: this.points,
    //                 pickName: pickPointName,
    //                 namesToAvoid: [],
    //             },
    //         ])
    //     )[0];
    //     for (let i = 0; i < this.points.length; i++) {
    //         const p = this.points[i];
    //         if (!p.name) {
    //             p.name = inputPointNames[i];
    //             const constructionPoint = this.buildPointOnCircleConstruction(
    //                 p.name,
    //                 this.circle.name,
    //                 angles[i - 1]
    //             );
    //             constructionPoints.push(constructionPoint);
    //         }
    //     }
    //     const name = `${this.points[0].name}${this.points[1].name}${this.points[2].name}`;
    //     const constructionCS = this.buildCSOnCircleConstruction(
    //         name,
    //         this.circle.name,
    //         this.points[1].name,
    //         this.points[2].name
    //     );
    //     this.resetState();
    //     await ctrl.editor.awarenessFeature.useAwareness(
    //         ctrl.viewport.id,
    //         'Đang tạo cung tròn',
    //         buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
    //         async () => {
    //             const constructResponse: ApplyConstructionResponse = await constructExec(() =>
    //                 this.editor.geoGateway.construct(docGlobalId, [
    //                     ...constructionPoints.map(
    //                         c =>
    //                             <ConstructionRequest>{
    //                                 construction: c,
    //                             }
    //                     ),
    //                     {
    //                         construction: constructionCS,
    //                     },
    //                 ])
    //             );
    //             await syncRenderCommands(constructResponse.render, ctrl);
    //             await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
    //         }
    //     );
    // }
    // }
}
