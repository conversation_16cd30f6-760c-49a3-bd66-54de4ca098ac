import { point, vector } from '@flatten-js/core';
import { ErrorHandlerDecorator, pointerTypeDyn, pointerTypeMouse, pointerTypePen } from '@viclass/editor.core';
import { syncPreviewCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewEllipseShape,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { GeometryTool } from './geo.tool';
import {
    buildPreviewVertexRenderProp,
    distance2Point,
    getPointAndVertex,
    handleIfPointerNotInError,
} from './tool.utils';

export class CreateEllipseTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateEllipseTool';

    private shape: PreviewEllipseShape | undefined;
    private points: RenderVertex[] = [];
    private isPointerDown = false;

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return (
            el.type == 'RenderVertex' &&
            (this.points[0]?.relIndex == el.relIndex || this.points.filter(p => p.relIndex == el.relIndex).length < 1)
        );
    };

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateEllipseTool> =
        new PotentialSelectionDelegator(this);
    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }

        event.nativeEvent.preventDefault();
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer down is already set

        const { ctrl, coords, vertex } = getPointAndVertex(this, event);
        this.points.push(vertex);

        if (this.points.length == 1) {
            syncPreviewCommands(vertex, ctrl);
            this.started = true;
        } else if (this.points.length == 2) {
            // Second point added, now we can preview the ellipse
            const current = coords;
            this.previewEllipse(
                ctrl,
                this.points[0].coords as [number, number],
                this.points[1].coords as [number, number],
                current as [number, number]
            );
        }

        this.isPointerDown = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        // if (!this.shouldHandleClick(event) || !this.points.length) return;
        // event.continue = false;
        // event.nativeEvent.preventDefault();
        // if (!this.isPointerDown) return; // don't handle if pointer down is not set
        // this.isPointerDown = false;
        // this.potentialSelectionDelegator.clearPotential();
        // if (this.points.length == 3) {
        //     const { ctrl } = this.posAndCtrl(event);
        //     // submit construction
        //     const constructionPoints: GeoElConstructionRequest[] = [];
        //     const inputEleNames = await requestElementNames(ctrl, nt, [
        //         {
        //             objName: 'Tiêu Điểm',
        //             originElement: this.points,
        //             pickName: pickPointName,
        //             namesToAvoid: [],
        //         },
        //         {
        //             objName: 'Đường Ellipse',
        //             originElement: [this.shape],
        //             pickName: pickShapeName,
        //             namesToAvoid: [],
        //         },
        //     ]);
        //     const inputPointNames = inputEleNames[0];
        //     const inputShapeNames = inputEleNames[1];
        //     if (!inputPointNames.length) {
        //         this.resetState();
        //         return;
        //     }
        //     for (let i = 0; i < this.points.length; i++) {
        //         const p = this.points[i];
        //         if (!p.name) {
        //             p.name = inputPointNames[i];
        //             const constructionPoint = buildPointConstruction(p.name, {
        //                 x: p.coords[0],
        //                 y: p.coords[1],
        //             });
        //             constructionPoints.push(constructionPoint);
        //         }
        //     }
        //     // calculate a, b, c giống backend
        //     const f1 = this.points[0].coords;
        //     const f2 = this.points[1].coords;
        //     const f3 = this.points[2].coords;
        //     const c = distance2Point(f1, f2) / 2;
        //     const d1 = distance2Point(f3, f1);
        //     const d2 = distance2Point(f3, f2);
        //     const a = (d1 + d2) / 2;
        //     const b = Math.sqrt(a * a - c * c);
        //     const constructionEllipse = this.buildEllipseConstruction(
        //         inputShapeNames[0],
        //         this.points[0].name,
        //         this.points[1].name,
        //         this.points[2].name
        //     );
        //     this.resetState();
        //     await ctrl.editor.awarenessFeature.useAwareness(
        //         ctrl.viewport.id,
        //         'Đang tạo hình ê líp',
        //         buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        //         async () => {
        //             const constructResponse = await constructExec(() =>
        //                 this.editor.geoGateway.construct(ctrl.state.globalId, [
        //                     ...constructionPoints.map(
        //                         c =>
        //                             <ConstructionRequest>{
        //                                 construction: c,
        //                             }
        //                     ),
        //                     {
        //                         construction: constructionEllipse,
        //                     },
        //                 ])
        //             );
        //             await syncRenderCommands(constructResponse.render, ctrl);
        //             await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
        //         }
        //     );
        // }
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (!this.points.length) return;
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.points.length < 1) return;

        const { ctrl, vertex, coords } = getPointAndVertex(this, event);

        if (this.isPointerDown && this.points.length >= 1) {
            if (this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) {
                const lastIdx = this.points.length - 1;
                this.points[lastIdx] = vertex;

                if (this.points.length == 1) this.previewPoint(ctrl, vertex);
            }
        }

        if (this.points.length >= 2) {
            // Moving after second point, show ellipse preview
            const current = this.isPointerDown && this.points.length > 2 ? this.points[2].coords : coords;
            this.previewEllipse(
                ctrl,
                this.points[0].coords as [number, number],
                this.points[1].coords as [number, number],
                current as [number, number]
            );
        }
    }

    private previewPoint(ctrl: GeoDocCtrl, vertex: RenderVertex) {
        const preview: RenderVertex = {
            relIndex: -10,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            name: undefined,
            coords: vertex.coords,
            usable: true,
            valid: true,
        };
        syncPreviewCommands(preview, ctrl);
    }

    private previewEllipse(
        ctrl: GeoDocCtrl,
        f1Coords: [number, number],
        f2Coords: [number, number],
        currentCoords: [number, number]
    ) {
        const f1 = [f1Coords[0], f1Coords[1], 0];
        const f2 = [f2Coords[0], f2Coords[1], 0];
        const f3 = [currentCoords[0], currentCoords[1], 0];

        const c = distance2Point(f1, f2) / 2;
        const d1 = distance2Point(f3, f1);
        const d2 = distance2Point(f3, f2);
        const a = (d1 + d2) / 2;
        const b = Math.sqrt(a * a - c * c);

        const v = vector(point(f1[0], f1[1]), point(f2[0], f2[1]));
        // if (v.length > 0 && !isNaN(b)) {
        //     this.shape = {
        //         relIndex: -20,
        //         type: 'RenderEllipseShape',
        //         elType: 'Ellipse',
        //         f1: f1,
        //         f2: f2,
        //         a: a,
        //         b: b,
        //         rotate: vector(1, 0).angleTo(v),
        //         name: '',
        //         renderProp: buildPreviewCircleShapeRenderProp(),
        //         usable: true,
        //         valid: true,
        //     };
        //     syncPreviewCommands(this.shape, ctrl);
        // }
    }

    // Xây dựng construction giống backend: truyền vào 3 tên điểm
    private buildEllipseConstruction(name: string, f1: string, f2: string, f3: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Ellipse/EllipseEC', 'Ellipse', 'Points');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-EllipseFocusPoints',
                params: {
                    name: {
                        type: 'array',
                        values: [f1, f2, f3],
                    },
                },
            },
        ];

        return construction;
    }
}
