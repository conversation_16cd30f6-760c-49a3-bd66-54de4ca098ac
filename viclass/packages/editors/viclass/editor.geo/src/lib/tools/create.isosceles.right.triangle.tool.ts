import { ErrorH<PERSON>lerDecorator, pointerTypeDyn, pointerTypeMouse, pointerTypePen } from '@viclass/editor.core';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, GeoRenderElement, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { GeometryTool } from './geo.tool';
import { getPointAndVertex, handleIfPointerNotInError } from './tool.utils';

export class CreateIsoscelesRightTriangleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateIsoscelesRightTriangleTool';

    private points: RenderVertex[] = [];
    private previewPoints: RenderVertex[] = [];
    private isPointerDown = false;
    private selectedPreviewPoint: RenderVertex | null = null;

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        // Prevent selecting the same vertex multiple times
        return el.type === 'RenderVertex' && this.points.filter(p => p.relIndex === el.relIndex).length < 1;
    };

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateIsoscelesRightTriangleTool> =
        new PotentialSelectionDelegator(this);

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.previewPoints = [];
        this.isPointerDown = false;
        this.selectedPreviewPoint = null;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown':
                this.onPointerDown(event);
                break;
            case 'pointerup':
                this.onPointerUp(event);
                break;
            case 'pointermove':
                this.onPointerMove(event);
                break;
        }
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return;
        this.isPointerDown = true;

        if (this.points.length === 0) {
            await this.handleFirstPoint(event);
        } else if (this.points.length === 1) {
            await this.handleSecondPoint(event);
        } else if (this.points.length === 2 && this.previewPoints.length > 0) {
            // Second point already defined, now selecting the third point's position from previews
            await this.selectThirdPointPosition(event, false); // false: not just a preview, it's a selection
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;
        this.potentialSelectionDelegator.clearPotential();

        // For the third point, we need another pointerdown/up pair to finalize
        if (this.points.length == 2 && this.selectedPreviewPoint) {
            await this.selectThirdPointPosition(event, false);
            await this.finalizeTriangle(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[0] = vertex;

        const v1 = vertex.coords;
        this.previewTriangle(ctrl, [v1, v1]);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[1] = vertex; // This is P1, the right-angle vertex

        this.updateThirdPointPreviews(ctrl, this.points[0], this.points[1]);

        if (this.points.some(p => !p.name)) {
            this.editor.filterElementFunc = () => false;
        }
        // Preview the leg P0-P1
        this.previewTriangle(ctrl, [this.points[0].coords, this.points[1].coords, this.points[1].coords]);
    }

    /**
     * Calculates and previews the two possible third vertices (P2) for the isosceles right triangle.
     * P0 (p0) is an endpoint of one leg, P1 (p1) is the common vertex of the two equal legs (the right-angle vertex).
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private updateThirdPointPreviews(ctrl: GeoDocCtrl, p0: RenderVertex, p1: RenderVertex) {
        // this.previewPoints = [];
        // const vP0 = point(p0.coords[0], p0.coords[1]);
        // const vP1 = point(p1.coords[0], p1.coords[1]);
        // if (vP0.equalTo(vP1)) {
        //     // Points coincide, cannot form a triangle leg
        //     syncPreviewCommands(null, ctrl); // Clear any existing preview points
        //     return;
        // }
        // const vec_P0_P1 = vector(vP0, vP1); // Vector from P0 to P1
        // // Perpendicular vectors with the same length as P0P1
        // const vec_perp1 = vec_P0_P1.rotate90CW(); // (vy, -vx)
        // const vec_perp2 = vec_P0_P1.rotate90CCW(); // (-vy, vx)
        // const p2_candidate_coords1 = [vP1.x + vec_perp1.x, vP1.y + vec_perp1.y];
        // const p2_candidate_coords2 = [vP1.x + vec_perp2.x, vP1.y + vec_perp2.y];
        // let relIdx = -12;
        // const candidateVertices: RenderVertex[] = [p2_candidate_coords1, p2_candidate_coords2].map(
        //     coords =>
        //         ({
        //             type: 'RenderVertex',
        //             renderProp: buildPreviewVertexRenderProp(),
        //             coords: coords,
        //             usable: true,
        //             valid: true,
        //             unselectable: true,
        //             name: undefined,
        //             relIndex: relIdx--,
        //         }) as RenderVertex
        // );
        // // Order the preview points consistently using main vector P0->P1
        // const mainVecArray = [vec_P0_P1.x, vec_P0_P1.y];
        // const p0CoordsForNth = [p0.coords[0], p0.coords[1], 0];
        // const nthOrder = nthDirectionByLine(mainVecArray, p0CoordsForNth, [
        //     candidateVertices[0].coords[0],
        //     candidateVertices[0].coords[1],
        //     0,
        // ]);
        // if (nthOrder === 1) {
        //     this.previewPoints.push(candidateVertices[0], candidateVertices[1]);
        // } else {
        //     this.previewPoints.push(candidateVertices[1], candidateVertices[0]);
        // }
        // this.previewPoints.forEach(pv => syncPreviewCommands(pv, ctrl));
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async selectThirdPointPosition(event: GeoPointerEvent, isPreview = true) {
        const { ctrl, pos } = this.posAndCtrl(event);

        if (this.points.length < 2 || this.previewPoints.length < 2) return;

        const pointerCoords = [pos.x, pos.y];
        const dist0 = Math.hypot(
            this.previewPoints[0].coords[0] - pointerCoords[0],
            this.previewPoints[0].coords[1] - pointerCoords[1]
        );
        const dist1 = Math.hypot(
            this.previewPoints[1].coords[0] - pointerCoords[0],
            this.previewPoints[1].coords[1] - pointerCoords[1]
        );

        const selectedThirdPointCandidate = dist0 <= dist1 ? this.previewPoints[0] : this.previewPoints[1];

        // Preview the full triangle: P0-P1-SelectedP2
        this.previewTriangle(ctrl, [this.points[0].coords, this.points[1].coords, selectedThirdPointCandidate.coords]);

        if (!isPreview) {
            this.selectedPreviewPoint = selectedThirdPointCandidate;
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeTriangle(event: GeoPointerEvent) {
        // const { ctrl, docGlobalId } = this.posAndCtrl(event);
        // const thirdVertex = this.selectedPreviewPoint; // This is P2
        // if (!thirdVertex || this.points.length < 2) return;
        // let constructionPoints: GeoElConstructionRequest[] = [];
        // // Points array for naming: P0, P1, P2
        // const allPointsForNaming = [...this.points, thirdVertex];
        // const inputPointNames = (
        //     await requestElementNames(ctrl, nt, [
        //         {
        //             objName: 'Tam Giác Vuông Cân',
        //             originElement: allPointsForNaming,
        //             pickName: pickPointName,
        //             namesToAvoid: [],
        //         },
        //     ])
        // )[0];
        // if (!inputPointNames) {
        //     this.resetState();
        //     return;
        // }
        // // Create constructions for P0 and P1 if they are new
        // for (let i = 0; i < this.points.length; i++) {
        //     const p = this.points[i];
        //     if (!p.name) {
        //         p.name = inputPointNames[i];
        //         constructionPoints.push(buildPointConstruction(p.name, { x: p.coords[0], y: p.coords[1] }));
        //     }
        // }
        // // The third vertex (P2) is always new from preview, assign its name
        // const p2Name = inputPointNames[2];
        // const triangleName = `${this.points[0].name}${this.points[1].name}${p2Name}`;
        // const p0_coords_3d = [this.points[0].coords[0], this.points[0].coords[1], 0];
        // const p1_coords_3d = [this.points[1].coords[0], this.points[1].coords[1], 0];
        // let nth = 1; // Default to the first orientation
        // if (
        //     this.previewPoints.length === 2 &&
        //     thirdVertex.coords[0] === this.previewPoints[1].coords[0] &&
        //     thirdVertex.coords[1] === this.previewPoints[1].coords[1]
        // ) {
        //     nth = 2; // Selected point matches the second orientation in previewPoints
        // }
        // // Use only one construction: always use FromTwoPosition
        // const constructionTriangle = this.buildTriangleFromTwoPositionConstruction(
        //     triangleName,
        //     p0_coords_3d,
        //     p1_coords_3d,
        //     nth
        // );
        // // When using FromTwoPosition, the first two points are position parameters, no separate points needed for P0, P1.
        // constructionPoints = constructionPoints.filter(
        //     cp => cp.name !== this.points[0].name && cp.name !== this.points[1].name
        // );
        // this.resetState(); // Reset before async operations
        // await ctrl.editor.awarenessFeature.useAwareness(
        //     ctrl.viewport.id,
        //     'Đang tạo tam giác vuông cân', // Leaving this string as it is.
        //     buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        //     async () => {
        //         const requests: ConstructionRequest[] = [{ construction: constructionTriangle }];
        //         const constructResponse = await constructExec(() =>
        //             this.editor.geoGateway.construct(docGlobalId, requests)
        //         );
        //         await syncRenderCommands(constructResponse.render, ctrl);
        //         await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
        //     }
        // );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length === 0) return; // Nothing to do if no points started

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        const { vertex, ctrl } = getPointAndVertex(this, event);

        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;

            // Update last point while dragging
            if (this.points.length === 1) {
                const lastIndex = this.points.length - 1;
                this.points[lastIndex] = vertex;

                const v1 = vertex.coords;
                this.previewTriangle(ctrl, [v1, v1]);
            } else if (this.points.length === 2 && this.previewPoints.length > 0) {
                // If we're in the third point selection phase, update selected preview point
                // when pointer moves while down
                this.selectThirdPointPosition(event, true);
            }
        } else {
            // Preview for next point
            if (this.points.length == 1) {
                const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
                const v = vertex.coords;
                this.previewTriangle(ctrl, [v1, v]);
            } else if (this.points.length == 2 && this.previewPoints.length > 0) {
                this.selectThirdPointPosition(event, true);
            }
        }
    }

    private previewTriangle(ctrl: GeoDocCtrl, facesCoords: number[][]) {
        // const polygon: PreviewPolygon = {
        //     relIndex: -20,
        //     name: '',
        //     type: 'RenderPolygon',
        //     elType: 'IsoscelesRightTriangle',
        //     verts: facesCoords,
        //     renderProp: buildPreviewPolygonRenderProp(),
        //     usable: true,
        //     valid: true,
        // };
        // syncPreviewCommands(polygon, ctrl);
    }

    /**
     * Use only one construction: FromTwoPosition
     * P0 (pos1) is a vertex of one leg.
     * P1 (pos2) is the right-angle vertex of the isosceles right triangle.
     * @param triangleName Name of the triangle.
     * @param pos1 Coordinates of P0.
     * @param pos2 Coordinates of P1 (right-angle vertex).
     * @param nth Orientation number (1 or 2).
     */
    private buildTriangleFromTwoPositionConstruction(
        triangleName: string,
        pos1: number[], // P0
        pos2: number[], // P1 (right-angle vertex)
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'IsoscelesRightTriangle/IsoscelesRightTriangleEC',
            'IsoscelesRightTriangle',
            'FromTwoPosition'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                // P0
                indexInCG: 0,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: { value: { type: 'array', values: pos1 } },
            },
            {
                // P1 (right-angle vertex)
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: { value: { type: 'array', values: pos2 } },
            },
            {
                // nth orientation
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: { value: { type: 'singleValue', value: nth } },
            },
        ];
        return construction;
    }
}
