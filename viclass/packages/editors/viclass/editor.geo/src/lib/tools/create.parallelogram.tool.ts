import Flatten from '@flatten-js/core';
import { ErrorHandlerDecorator, pointerTypeDyn, pointerTypeMouse, pointerTypePen } from '@viclass/editor.core';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, GeoRenderElement, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { GeometryTool } from './geo.tool';
import { getPointAndVertex, handleIfPointerNotInError, isDifferentCoords } from './tool.utils';
import point = Flatten.point;
import line = Flatten.line;

export class CreateParallelogramTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateParallelogramTool';

    private points: RenderVertex[] = [];
    private isPointerDown = false;

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateParallelogramTool> =
        new PotentialSelectionDelegator(this);

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        if (this.points.length == 0) {
            await this.handleFirstPoint(event);
        } else if (this.points.length == 1) {
            await this.handleSecondPoint(event);
        } else if (this.points.length == 2) {
            await this.handleThirdPoint(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length == 3) {
            await this.finalizeParallelogram(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        if (this.points.length > 1) return; // handle 0 or 1 point
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[0] = vertex; // add/update first point

        const v1 = vertex.coords;
        this.previewParallelogram(ctrl, v1, v1);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        if (this.points.length > 2 || this.points.length < 1) return; // handle 1 or 2 points
        const { ctrl, vertex } = getPointAndVertex(this, event);
        if (isDifferentCoords(vertex.coords, this.points[0].coords)) {
            this.points[1] = vertex; // add/update 2nd point when it not match the first point
        }
        if (this.points.length !== 2) return;

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        this.previewParallelogram(ctrl, v1, v2);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleThirdPoint(event: GeoPointerEvent) {
        if (this.points.length > 3 || this.points.length < 2) return; // handle 2 or 3 points
        const { ctrl, vertex } = getPointAndVertex(this, event);
        if (
            isDifferentCoords(vertex.coords, this.points[0].coords) &&
            isDifferentCoords(vertex.coords, this.points[1].coords)
        ) {
            this.points[2] = vertex; // add/update 3rd point when it not match the previous points
        }
        if (this.points.length !== 3) return;

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        const v3 = [vertex.coords[0], vertex.coords[1], 0];

        // Calculate fourth point based on parallelogram properties
        const p = point(v3[0], v3[1]);
        const p1 = point(v1[0], v1[1]);
        const p2 = point(v2[0], v2[1]);
        const linep1p2 = line(p1, p2);
        const linep2p = line(p2, p);
        const parallelLine2 = line(p, linep1p2.norm);
        const parallelLine1 = line(p1, linep2p.norm);
        const intersection = parallelLine1.intersect(parallelLine2)[0];
        const v4 = [intersection.x, intersection.y, 0.0];

        this.previewParallelogram(ctrl, v1, v2, v3, v4);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeParallelogram(event: GeoPointerEvent) {
        // const { ctrl, docGlobalId } = this.posAndCtrl(event);
        // // Calculate the fourth point
        // const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        // const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        // const v3 = [this.points[2].coords[0], this.points[2].coords[1], 0];
        // const p = point(v3[0], v3[1]);
        // const p1 = point(v1[0], v1[1]);
        // const p2 = point(v2[0], v2[1]);
        // const linep1p2 = line(p1, p2);
        // const linep2p = line(p2, p);
        // const parallelLine2 = line(p, linep1p2.norm);
        // const parallelLine1 = line(p1, linep2p.norm);
        // const intersection = parallelLine1.intersect(parallelLine2)[0];
        // const v4 = [intersection.x, intersection.y, 0.0];
        // const vertex4: RenderVertex = {
        //     relIndex: -21,
        //     type: 'RenderVertex',
        //     elType: 'Point',
        //     renderProp: buildPreviewVertexRenderProp(),
        //     name: undefined,
        //     coords: v4,
        //     usable: true,
        //     valid: true,
        // };
        // // Submit construction
        // const constructionPoints: GeoElConstructionRequest[] = [];
        // const inputPointNames = (
        //     await requestElementNames(ctrl, nt, [
        //         {
        //             objName: 'Hình Bình Hành',
        //             originElement: this.points.concat(vertex4),
        //             pickName: pickPointName,
        //             namesToAvoid: [],
        //         },
        //     ])
        // )[0];
        // if (!inputPointNames.length) {
        //     this.resetState();
        //     return;
        // }
        // for (let i = 0; i < this.points.length; i++) {
        //     const p = this.points[i];
        //     if (!p.name) {
        //         p.name = inputPointNames[i];
        //         const constructionPoint = buildPointConstruction(p.name, {
        //             x: p.coords[0],
        //             y: p.coords[1],
        //         });
        //         constructionPoints.push(constructionPoint);
        //     }
        // }
        // const lineName = `${this.points[0].name}${this.points[1].name}`;
        // const parallelogramName = `${lineName}${inputPointNames[2]}${inputPointNames[3]}`;
        // let constructionParallelogram: GeoElConstructionRequest;
        // if (this.points.length == 3) {
        //     constructionParallelogram = this.buildParallelogramConstruction(parallelogramName);
        // } else if (this.points.length == 4) {
        //     constructionParallelogram = this.buildParallelogramFromPointsConstruction(this.points.map(p => p.name));
        // }
        // this.resetState();
        // await ctrl.editor.awarenessFeature.useAwareness(
        //     ctrl.viewport.id,
        //     'Đang tạo hình bình hành',
        //     buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        //     async () => {
        //         const constructResponse = await constructExec(() =>
        //             this.editor.geoGateway.construct(docGlobalId, [
        //                 ...constructionPoints.map(
        //                     c =>
        //                         <ConstructionRequest>{
        //                             construction: c,
        //                         }
        //                 ),
        //                 {
        //                     construction: constructionParallelogram,
        //                 },
        //             ])
        //         );
        //         await syncRenderCommands(constructResponse.render, ctrl);
        //         await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
        //     }
        // );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length == 0 || this.points.length > 3) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
            // while pointer down -> handle all 3 cases
            if (this.points.length === 1) {
                this.handleFirstPoint(event);
            } else if (this.points.length === 2) {
                this.handleSecondPoint(event);
            } else if (this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        } else {
            // other mouse move -> only handle preview for the third point
            if (this.points.length === 2 || this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        }
    }

    private buildParallelogramConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Parallelogram/ParallelogramEC',
            'Parallelogram',
            'ByPointsName'
        );
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }

    private buildParallelogramFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Parallelogram/ParallelogramEC',
            'Parallelogram',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }

    private async previewParallelogram(ctrl: GeoDocCtrl, ...faces: number[][]) {
        // const polygon: PreviewPolygon = {
        //     relIndex: -20,
        //     name: '',
        //     type: 'RenderPolygon',
        //     elType: 'Parallelogram',
        //     faces: faces,
        //     renderProp: buildPreviewPolygonRenderProp(),
        //     usable: true,
        //     valid: true,
        // };
        // await syncPreviewCommands(polygon, ctrl);
    }
}
