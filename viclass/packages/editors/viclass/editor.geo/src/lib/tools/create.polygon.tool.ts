import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderVector, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pPolygon, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { repeat, RepeatSelector, SelectedVertex, vert, vertexS } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, remoteConstruct } from './tool.utils';

export class CreatePolygonTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePolygonTool';

    declare selLogic: RepeatSelector<SelectedVertex>;
    pQ = new PreviewQueue();

    excludedPoints: RenderVertex[] = [];

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    createSelLogic() {
        this.selLogic = repeat<SelectedVertex>(vertexS(this.pQ, this.pointerHandler.cursor), {
            onPartialSelection: this.newPointSelected.bind(this),
            onComplete: this.performConstruction.bind(this),
        });
        this.selLogic
            .get('vertex')
            .setOption('preview', true)
            .setOption('refinedFilter', this.excludeSelection.bind(this));
    }

    excludeSelection(el: RenderVertex) {
        return !this.excludedPoints.map(p => p.relIndex).includes(el.relIndex);
    }

    newPointSelected(newSel: SelectedVertex, curSel: SelectedVertex[], selector: any, doc: GeoDocCtrl): boolean {
        console.log('New point selected', vert(newSel), doc.rendererCtrl.elementAt(vert(newSel).relIndex));
        if (curSel.length <= 2) {
            // when there are less 3 points, all currently selected point cannot be selected
            this.excludedPoints = curSel.map(s => vert(s));
        } else if (curSel.length == 3) {
            this.excludedPoints = [vert(curSel[1]), vert(curSel[2])]; // when there are 3 points, the last two points cannot be selected, first point can
        } else this.excludedPoints.push(vert(newSel)); // from 3 points onward, all new point selected cannot be reselected again

        let complete = false;
        if (curSel.length > 2 && vert(newSel).relIndex == vert(curSel[0]).relIndex) {
            // if new sel is the same as the first sel, then the polygon is completed
            complete = true;
        }
        return !complete;
    }

    override resetState() {
        this.selLogic.reset();
        this.excludedPoints = [];
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected && selected.length > 1) {
            const p = pPolygon(
                ctrl,
                -20,
                selected.map(s => vert(s).coords),
                true,
                RenderVector
            ); // we just draw the polygon without its vertices element, The individual element is synced by the vertex selector
            this.pQ.add(p);
        }

        this.pQ.flush(ctrl);
    }

    protected async performConstruction(selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
        selector.selected.splice(selector.selected.length - 1, 1); // remove the last point, because it is the same as the first point
        const { pcs, points, strokes } = await assignNames(
            ctrl,
            selector.selected,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Đa giác'
        );
        if (!pcs) {
            this.resetState();
            return;
        }
        const polygonName = points.map(p => p.name).join('');
        const constructionPolygon = this.buildPolygonConstruction(polygonName);

        try {
            await remoteConstruct(ctrl, constructionPolygon, pcs, this.editor.geoGateway, 'Đa giác');
        } finally {
            this.resetState();
        }
    }

    private buildPolygonConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Polygon/PolygonEC', 'Polygon', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }
}
