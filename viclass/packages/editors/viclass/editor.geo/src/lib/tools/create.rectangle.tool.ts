import Flatten from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects/geo.document.ctrl';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewVertexRenderProp,
    createVector,
    getPointAndVertex,
    handleIfPointerNotInError,
    isDifferentCoords,
    pickPointName,
    requestElementNames,
} from './tool.utils';
import point = Flatten.point;
import line = Flatten.line;
import vector = Flatten.vector;

export class CreateRectangleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateRectangleTool';

    private points: RenderVertex[] = [];
    private linePerpendicular1: Flatten.Line;
    private linePerpendicular2: Flatten.Line;
    private isPointerDown = false;

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateRectangleTool> =
        new PotentialSelectionDelegator(this);

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.linePerpendicular1 = undefined;
        this.linePerpendicular2 = undefined;
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        if (this.points.length == 0) {
            await this.handleFirstPoint(event);
        } else if (this.points.length == 1) {
            await this.handleSecondPoint(event);
        } else if (this.points.length == 2) {
            await this.handleThirdPoint(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length == 2) {
            this.editor.filterElementFunc = el => false;
        }
        if (this.points.length == 3) {
            await this.finalizeRectangle(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        if (this.points.length > 1) return; // handle 0 or 1 point
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[0] = vertex; // add/update first point

        this.previewRectangle(ctrl, vertex.coords, vertex.coords);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        if (this.points.length > 2 || this.points.length < 1) return; // handle 1 or 2 points
        const { ctrl, vertex } = getPointAndVertex(this, event);
        if (isDifferentCoords(vertex.coords, this.points[0].coords)) {
            this.points[1] = vertex; // add/update 2nd point when it not match the first point
        }
        if (this.points.length !== 2) return;

        const vertex1 = this.points[0];
        const vertex2 = this.points[1];
        const p1 = point(vertex1.coords[0], vertex1.coords[1]);
        const p2 = point(vertex2.coords[0], vertex2.coords[1]);

        this.linePerpendicular1 = line(p2, vector(p2, p1));
        this.linePerpendicular2 = line(p1, vector(p2, p1));

        this.previewRectangle(ctrl, vertex1.coords, vertex2.coords);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleThirdPoint(event: GeoPointerEvent) {
        if (this.points.length > 3 || this.points.length < 2) return; // handle 2 or 3 points
        const { ctrl, vertex, coords } = getPointAndVertex(this, event);
        this.points[2] = vertex; // add/update third point

        // Calculate rectangle points based on the third point
        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

        const p = point(coords[0], coords[1]);
        const projection1 = p.projectionOn(this.linePerpendicular1);
        const projection2 = p.projectionOn(this.linePerpendicular2);
        const v3 = [projection1.x, projection1.y, 0.0];
        const v4 = [projection2.x, projection2.y, 0.0];

        this.previewRectangle(ctrl, v1, v2, v3, v4);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeRectangle(event: GeoPointerEvent) {
        const { ctrl, pos, docGlobalId } = this.posAndCtrl(event);

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

        const p = point(pos.x, pos.y);
        const projection1 = p.projectionOn(this.linePerpendicular1);
        const projection2 = p.projectionOn(this.linePerpendicular2);
        const v3 = [projection1.x, projection1.y, 0.0];
        const v4 = [projection2.x, projection2.y, 0.0];

        const vP12 = createVector(this.points[0], this.points[1]);
        const nth = nthDirectionByLine(vP12, v1, v3);

        // create the final points 3 and 4 of the rectangle
        const vertex3: RenderVertex = {
            relIndex: -12,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            coords: v3,
            name: undefined,
            usable: true,
            valid: true,
        };
        const vertex4: RenderVertex = {
            relIndex: -13,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            coords: v4,
            name: undefined,
            usable: true,
            valid: true,
        };
        this.points[2] = vertex3;
        this.points[3] = vertex4;

        // Check which points need to be constructed
        const constructionPoints: GeoElConstructionRequest[] = [];
        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Hình Chữ Nhật',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        // Determine which construction method to use
        let constructionRectangle: GeoElConstructionRequest;

        if (this.points.length === 4) {
            constructionRectangle = this.buildRectangleFromPointsConstruction(this.points.map(p => p.name));
        } else {
            const lineName = `${this.points[0].name}${this.points[1].name}`;
            const l = line(point(v1[0], v1[1]), point(v2[0], v2[1]));
            const height = p.distanceTo(l)[0];

            const rectangleName = this.points.map(p => p.name).join('');
            constructionRectangle = this.buildRectangleFromLineSegmentAndLengthConstruction(
                rectangleName,
                lineName,
                height,
                nth
            );
        }

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình chữ nhật',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docGlobalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionRectangle,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length == 0 || this.points.length == 4) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
            // while pointer down -> handle all 3 cases
            if (this.points.length === 1) {
                this.handleFirstPoint(event);
            } else if (this.points.length === 2) {
                this.handleSecondPoint(event);
            } else if (this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        } else {
            // other mouse move -> only handle preview for the third point
            if (this.points.length === 2 || this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        }
    }

    private async previewRectangle(ctrl: GeoDocCtrl, ...faces: number[][]) {
        // const polygon: PreviewPolygon = {
        //     relIndex: -20,
        //     name: '',
        //     type: 'RenderPolygon',
        //     elType: 'Rectangle',
        //     faces: faces,
        //     renderProp: buildPreviewPolygonRenderProp(),
        //     usable: true,
        //     valid: true,
        // };
        // await syncPreviewCommands(polygon, ctrl);
    }

    private buildRectangleFromLineSegmentAndLengthConstruction(
        rectangleName: string,
        lineName: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'LineSegmentAndLength');
        construction.name = rectangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-LengthValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildRectangleFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}
