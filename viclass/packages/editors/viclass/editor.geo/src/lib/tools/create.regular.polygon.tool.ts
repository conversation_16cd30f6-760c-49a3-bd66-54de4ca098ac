import { MouseEventData, UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, RegularPolygonToolState, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { repeat, RepeatSelector, SelectedVertex, then, ThenSelector, vertex, vertexS } from '../selectors';
import { GeometryTool } from './geo.tool';
import { getFocusDocCtrl, handleIfPointerNotInError } from './tool.utils';

const EDGE_MIN = 3;
const EDGE_MAX = 8;

/**
 * Check if the number of edges is valid (an integer within the allowed range)
 */
function isValidEdge(noEdge: number) {
    return Number.isSafeInteger(noEdge) && noEdge >= EDGE_MIN && noEdge <= EDGE_MAX;
}

/**
 * Clamp the number of edges of the regular polygon within [3, 8] and round to the nearest integer.
 */
function clampEdge(noEdge: number) {
    return Math.min(Math.max(Math.round(noEdge) || EDGE_MIN, EDGE_MIN), EDGE_MAX);
}

/**
 * Tool for creating regular polygons. Allows users to draw a regular polygon by clicking on the canvas
 * and change the number of edges by scrolling the mouse wheel while holding Ctrl.
 */
export class CreateRegularPolygonTool extends GeometryTool<RegularPolygonToolState> {
    readonly toolType: GeometryToolType = 'CreateRegularPolygonTool';

    declare selLogic?: ThenSelector;
    pQ = new PreviewQueue();

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        // Register mouse wheel event to change the number of edges
        this.registerMouseHandling(
            {
                event: 'mousewheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            },
            {
                event: 'wheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            }
        );
        this.noEdge = this.toolState.noEdge;

        this.createSelLogic();
    }

    private createSelLogic() {
        this.selLogic = then(
            [
                // first select two points, the first two points can be on an edge or existing points or free points
                repeat<SelectedVertex>(vertexS(this.pQ, this.pointerHandler.cursor), {
                    count: 2,
                    onComplete: this.first2Points.bind(this),
                }),
                repeat<RenderVertex>(
                    vertex({
                        genPreview: false,
                        previewQueue: this.pQ,
                        cursor: this.pointerHandler.cursor,
                        cfunc: this.checkAdditionalPoints.bind(this),
                    }),
                    {
                        onPartialSelection: this.additionalPoints.bind(this),
                    }
                ),
            ],
            { flatten: 1 } // unbox the repeat  level so we have an array of SelectedVertex | RenderVertex type
        );
    }

    first2Points(selector: RepeatSelector<SelectedVertex>, doc: GeoDocCtrl) {}

    additionalPoints(newSel: RenderVertex, curSel: RenderVertex[]) {
        return true;
    }

    checkAdditionalPoints(v: RenderVertex): boolean {
        return true;
    }

    performConstruction(selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {}

    set noEdge(noEdge: number) {
        if (isValidEdge(noEdge)) this.toolState.noEdge = noEdge;
        else throw new Error('Invalid number of edges for regular polygon');

        this.toolbar.update('CreateRegularPolygonTool', this.toolState);
    }

    /**
     * Reset the tool state to be ready to create a new polygon.
     * Clear the list of points, reset relIndex, nth, pointer down flag, click count.
     * Call resetState of the parent class.
     */
    override resetState() {
        this.toolbar.update('CreateRegularPolygonTool', new RegularPolygonToolState());
        this.selLogic.reset();
        super.resetState();
    }

    /**
     * Handle mouse events (mousewheel, wheel) to change the number of edges
     */
    override handleMouseEvent(event: MouseEventData<any>): MouseEventData<any> {
        switch (event.nativeEvent.type) {
            case 'mousewheel':
            case 'wheel': {
                if (this.started) {
                    this.onMouseWheel(event);
                    event.continue = false;
                }
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        // if (selected && selected.length >= 1) {
        //     if ((selected[0] as SelectedVe.length) == 2) const p0 = selected[0][0] as RenderVertex;
        //     const p1 = selected[0][1] as RenderVertex;

        //     const p = pPolygon(
        //         ctrl,
        //         -20,
        //         selected.map(s => vert(s).coords),
        //         true,
        //         RenderVector
        //     ); // we just draw the polygon without its vertices element, The individual element is synced by the vertex selector
        //     this.pQ.add(p);
        // }

        this.pQ.flush(ctrl);
    }

    // Handle non-UI pointer events (default)

    /**
     * Handle mouse wheel event to increase/decrease the number of edges of the regular polygon.
     * Scroll up: increase edges, scroll down: decrease edges.
     */
    private async onMouseWheel(event: MouseEventData<any>) {
        const wheel = (event.nativeEvent as WheelEvent).deltaY < 0 ? 1 : -1;

        this.noEdge = clampEdge(this.noEdge + wheel);
    }

    /**
     * Finalize the creation of the regular polygon based on the confirmed points and direction.
     */
    // @ErrorHandlerDecorator([geoDefaultHandlerFn])
    // private async finalizePolygon(event: GeoPointerEvent) {
    //     if (!this.isValid) {
    //         this.resetState();
    //         throw new InvalidUserInputErr('Invalid number of edges');
    //     }

    //     const { ctrl } = this.posAndCtrl(event);

    //     let constructionPoints: GeoElConstructionRequest[] = [];
    //     let constructionPolygon: GeoElConstructionRequest;

    //     // Calculate the remaining vertices of the regular polygon
    //     const points = this.calculateRemainingVertices();

    //     // Create RenderVertex objects for the remaining vertices (preview)
    //     const vertexes = points.map(
    //         coords =>
    //             <RenderVertex>{
    //                 relIndex: this.relIndex--,
    //                 type: 'RenderVertex',
    //                 renderProp: buildPreviewVertexRenderProp(),
    //                 name: undefined,
    //                 coords: coords,
    //                 usable: true,
    //                 valid: true,
    //             }
    //     );

    //     // Request input for point names (if needed)
    //     const inputPointNames = (
    //         await requestElementNames(ctrl, nt, [
    //             {
    //                 objName: 'Regular Polygon',
    //                 originElement: this.points.concat(...vertexes),
    //                 pickName: pickPointName,
    //                 namesToAvoid: [],
    //             },
    //         ])
    //     )[0];
    //     if (!inputPointNames) {
    //         this.resetState();
    //         return;
    //     }

    //     // Create construction points if the point does not have a name
    //     for (let i = 0; i < this.points.length; i++) {
    //         const p = this.points[i];
    //         if (!p.name) {
    //             p.name = inputPointNames[i];
    //             const constructionPoint = buildPointConstruction(p.name, {
    //                 x: p.coords[0],
    //                 y: p.coords[1],
    //             });
    //             constructionPoints.push(constructionPoint);
    //         }
    //     }

    //     const polygonName = inputPointNames.join('');
    //     if (constructionPoints.length < 2) {
    //         // If there are enough points, create the polygon from the line segment
    //         const lineName = this.points.map(p => p.name).join('');
    //         constructionPolygon = this.buildPolygonFromLineSegmentConstruction(
    //             polygonName,
    //             lineName,
    //             this.noEdge,
    //             this.nth
    //         );
    //     } else {
    //         // If not enough points, create the polygon from two positions
    //         constructionPolygon = this.buildPolygonFromTwoPositionConstruction(
    //             polygonName,
    //             this.points[0].coords,
    //             this.points[1].coords,
    //             this.noEdge,
    //             this.nth
    //         );
    //         constructionPoints = [];
    //     }

    //     this.resetState();

    //     // Execute the command to create the regular polygon and sync render, history
    //     await ctrl.editor.awarenessFeature.useAwareness(
    //         ctrl.viewport.id,
    //         'Creating regular polygon',
    //         buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
    //         async () => {
    //             const constructResponse = await constructExec(() =>
    //                 this.editor.geoGateway.construct(ctrl.state.globalId, [
    //                     ...constructionPoints.map(
    //                         c =>
    //                             <ConstructionRequest>{
    //                                 construction: c,
    //                             }
    //                     ),
    //                     {
    //                         construction: constructionPolygon,
    //                     },
    //                 ])
    //             );

    //             await syncRenderCommands(constructResponse.render, ctrl);
    //             await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
    //         }
    //     );
    // }

    /**
     * Calculate the remaining vertices of the regular polygon based on the first two points.
     * Returns an array of coordinates of the remaining vertices.
     */
    // private calculateRemainingVertices(): number[][] {
    //     if (this.points.length < 2) return [];

    //     const points: number[][] = [];
    //     const v1 = this.points[0].coords;
    //     const v2 = this.points[1].coords;

    //     const p1 = point(v1[0], v1[1]);
    //     const p2 = point(v2[0], v2[1]);
    //     const angle = (2 * Math.PI) / this.noEdge;
    //     const size = p1.distanceTo(p2)[0];
    //     const r = size / 2 / Math.sin(angle / 2);
    //     const c1 = circle(p1, r);
    //     const c2 = circle(p2, r);
    //     const c1_x_c2 = c1.intersect(c2);

    //     if (c1_x_c2.length === 0) return []; // No intersection, cannot create polygon

    //     const pC = c1_x_c2[this.nth == 1 ? 1 : 0];

    //     // Function to create a new point by rotating around center pC
    //     const buildPoint2 = (p0: Point, i: number): number[] => {
    //         const p = p0.rotate(angle * i, pC);
    //         return [p.x, p.y, 0.0];
    //     };

    //     for (let i = 1; i < this.noEdge - 1; i += 1) {
    //         points.push(buildPoint2(p2, this.nth == 1 ? i : -i));
    //     }

    //     return points;
    // }

    /**
     * Create a construction request for a regular polygon from two positions (two points).
     */
    private buildPolygonFromTwoPositionConstruction(
        polygonName: string,
        pos1: number[],
        pos2: number[],
        noEdge: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromTwoPosition'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos1,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos2,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-NoEdge',
                params: {
                    value: {
                        type: 'singleValue',
                        value: noEdge,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    /**
     * Create a construction request for a regular polygon from a line segment (two named points).
     */
    private buildPolygonFromLineSegmentConstruction(
        polygonName: string,
        lineName: string,
        noEdge: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromLineSegment'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-NoEdge',
                params: {
                    value: {
                        type: 'singleValue',
                        value: noEdge,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    /**
     * Create a construction request for a regular polygon from multiple points (not used in main flow).
     */
    private buildPolygonFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}
