import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    KeyboardHandlingItem,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderVertex,
} from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    getPointAndVertex,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './tool.utils';

export class CreateSemicircleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateSemicircleTool';

    private points: RenderVertex[] = [];
    private clockwise: boolean = false;
    private isPointerDown = false;

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type === 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateSemicircleTool> =
        new PotentialSelectionDelegator(this);

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn },
            // shift modifiers
            { event: 'pointerdown', keys: ['shift'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', keys: ['shift'], pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointermove', keys: ['shift'], pointerTypes: pointerTypeMouse },
            { event: 'pointermove', keys: ['shift'], numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointerup', keys: ['shift'], button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', keys: ['shift'], pointerTypes: pointerTypePen, numPointer: 0 }
        );
        this.registerKeyboardHandling(
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keyup';
            })(['shift']),
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keydown';
            })(['shift'])
        );
    }

    override resetState() {
        this.points = [];
        this.clockwise = false;
        this.isPointerDown = false;
        super.resetState();
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        event = super.handleKeyboardEvent(event);
        if (event.nativeEvent.key == 'Shift') {
            if (event.eventType == 'keydown') {
                this.clockwise = true;
                this.onPointerMove(this.lastPointerMove);
            } else if (event.eventType == 'keyup') {
                this.clockwise = false;
                this.onPointerMove(this.lastPointerMove);
            }
        }
        return event;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.lastPointerMove = event;
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }

        event.nativeEvent.preventDefault();
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer down is already set

        const { ctrl, coords, vertex } = getPointAndVertex(this, event);
        this.points.push(vertex);

        if (this.points.length == 1) {
            this.previewSemicircle(ctrl, coords, coords);
            this.started = true;
        }

        this.isPointerDown = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event) || !this.points.length) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length < 2) return;
        const { ctrl, docGlobalId } = this.posAndCtrl(event);

        const centerCoords = [
            (this.points[0].coords[0] + this.points[1].coords[0]) / 2,
            (this.points[0].coords[1] + this.points[1].coords[1]) / 2,
        ];
        let points = [this.points[0], <RenderVertex>{ coords: centerCoords }, this.points[1]];

        // submit construction
        const constructionPoints: GeoElConstructionRequest[] = [];
        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Bán Nguyệt',
                    originElement: points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < points.length; i++) {
            const p = points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        if (this.clockwise) points = points.reverse();

        const semicircleName = points.map(p => p.name).join('');
        const constructionSemicircle = this.buildSemicircleConstruction(semicircleName);

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo bán nguyệt',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docGlobalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionSemicircle,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (!this.points.length) return;
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (!this.points.length) return;

        if (this.isPointerDown && !this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;

        const { ctrl, vertex, coords } = getPointAndVertex(this, event);

        if (this.isPointerDown && this.points.length >= 1) {
            const lastIdx = this.points.length - 1;
            this.points[lastIdx] = vertex;
        }

        let v1 = this.points[0].coords;
        let v = this.isPointerDown && this.points.length > 1 ? this.points[1].coords : coords;

        if (this.clockwise) {
            const tmp = v1;
            v1 = v;
            v = tmp;
        }

        this.previewSemicircle(ctrl, v1, v);
    }

    private previewSemicircle(ctrl: GeoDocCtrl, v1: number[], v: number[]) {
        // const vCenter = [(v[0] + v1[0]) / 2, (v[1] + v1[1]) / 2, (v[2] + v1[2]) / 2];
        // const semicircle: PreviewSectorShape = {
        //     relIndex: -20,
        //     type: 'RenderSectorShape',
        //     elType: 'Semicircle',
        //     name: '',
        //     renderProp: buildPreviewSectorShapeRenderProp(),
        //     startPoint: v1,
        //     endPoint: v,
        //     centerPoint: vCenter,
        //     usable: true,
        //     valid: true,
        // };
        // syncPreviewCommands(semicircle, ctrl);
    }

    private buildSemicircleConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Semicircle/SemicircleEC', 'Semicircle', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }
}
