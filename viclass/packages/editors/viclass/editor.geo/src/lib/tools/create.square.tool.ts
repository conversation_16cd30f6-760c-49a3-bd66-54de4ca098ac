import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects/geo.document.ctrl';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewVertexRenderProp,
    getPointAndVertex,
    handleIfPointerNotInError,
    pickPointName,
    requestElementNames,
} from './tool.utils';

export class CreateSquareTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateSquareTool';

    private points: RenderVertex[] = [];
    private previewPoints: RenderVertex[][] = [];
    private isPointerDown = false;

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateSquareTool> =
        new PotentialSelectionDelegator(this);

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.previewPoints = [];
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        if (this.points.length == 0) {
            await this.handleFirstPoint(event);
        } else if (this.points.length == 1) {
            await this.handleSecondPoint(event);
        } else if (this.points.length == 2) {
            await this.handleThirdPoint(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length == 2) {
            this.editor.filterElementFunc = el => false;
        }

        if (this.points.length == 3) {
            await this.finalizeSquare(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        if (this.points.length > 1) return; // handle 0 or 1 point
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[0] = vertex; // add/update first point

        this.previewSquare(ctrl, vertex.coords, vertex.coords);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        // if (this.points.length > 2 || this.points.length < 1) return; // handle 1 or 2 points
        // const { ctrl, vertex } = getPointAndVertex(this, event);
        // if (isDifferentCoords(vertex.coords, this.points[0].coords)) {
        //     this.points[1] = vertex; // add/update 2nd point when it not match the first point
        // }
        // if (this.points.length !== 2) return;
        // const vertex1 = this.points[0];
        // const vertex2 = this.points[1];
        // this.previewSquare(ctrl, vertex1.coords, vertex2.coords);
        // const center1 = point(vertex1.coords[0], vertex1.coords[1]);
        // const center2 = point(vertex2.coords[0], vertex2.coords[1]);
        // const r = center1.distanceTo(center2)[0];
        // const c1 = circle(center1, r);
        // const c2 = circle(center2, r);
        // const linePerpendicular1 = line(center1, vector(center1, center2));
        // const linePerpendicular2 = line(center2, vector(center1, center2));
        // const intersections1 = linePerpendicular1.intersect(c1);
        // const intersections2 = linePerpendicular2.intersect(c2);
        // const intersections = [...intersections1, ...intersections2];
        // let idx = -12;
        // const ps = intersections.map(
        //     i =>
        //         ({
        //             type: 'RenderVertex',
        //             renderProp: buildPreviewVertexRenderProp(),
        //             coords: [i.x, i.y],
        //             usable: true,
        //             name: undefined,
        //             relIndex: idx--,
        //         }) as RenderVertex
        // );
        // // build preview points on both sides
        // this.previewPoints = [];
        // let nth: number;
        // nth = nthDirectionByLine([center2.x - center1.x, center2.y - center1.y], vertex1.coords, [
        //     ps[2].coords[0],
        //     ps[2].coords[1],
        // ]);
        // if (nth == 1) {
        //     this.previewPoints.push([ps[2], ps[3]]);
        // } else {
        //     this.previewPoints.push([ps[3], ps[2]]);
        // }
        // nth = nthDirectionByLine([center2.x - center1.x, center2.y - center1.y], vertex1.coords, [
        //     ps[0].coords[0],
        //     ps[0].coords[1],
        // ]);
        // if (nth == 1) {
        //     this.previewPoints.push([ps[0], ps[1]]);
        // } else {
        //     this.previewPoints.push([ps[1], ps[0]]);
        // }
        // this.previewPoints.forEach(a => a.forEach(p => syncPreviewCommands(p, ctrl)));
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleThirdPoint(event: GeoPointerEvent) {
        if (this.points.length > 3 || this.points.length < 2) return; // handle 2 or 3 points
        const { ctrl, vertex, coords } = getPointAndVertex(this, event);
        this.points[2] = vertex; // add/update third point

        // Will be finalized on next pointer up
        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        let v3 = coords;

        // Calculate 3rd, 4th point based on 3rd point choice
        const nth = nthDirectionByLine([v2[0] - v1[0], v2[1] - v1[1]], v1, v3);
        let v4: number[];

        if (nth === 1) {
            v3 = this.previewPoints[0][0].coords;
            v4 = this.previewPoints[1][0].coords;
        } else {
            v3 = this.previewPoints[0][1].coords;
            v4 = this.previewPoints[1][1].coords;
        }

        this.previewSquare(ctrl, v1, v2, v3, v4);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeSquare(event: GeoPointerEvent) {
        const { ctrl, pos, docGlobalId } = this.posAndCtrl(event);

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        let v3 = [pos.x, pos.y, 0.0];

        // Calculate 3rd, 4th point based on 3rd point choice
        const nth = nthDirectionByLine([v2[0] - v1[0], v2[1] - v1[1]], v1, v3);
        let v4: number[];

        if (nth === 1) {
            v3 = this.previewPoints[0][0].coords;
            v4 = this.previewPoints[1][0].coords;
        } else {
            v3 = this.previewPoints[0][1].coords;
            v4 = this.previewPoints[1][1].coords;
        }

        // create the final points 3 and 4 of the square
        const vertex3: RenderVertex = {
            relIndex: -13,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            coords: v3,
            name: undefined,
            usable: true,
            valid: true,
        };
        const vertex4: RenderVertex = {
            relIndex: -14,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            coords: v4,
            name: undefined,
            usable: true,
            valid: true,
        };
        this.points[2] = vertex3;
        this.points[3] = vertex4;

        // Check which points need to be constructed
        const constructionPoints: GeoElConstructionRequest[] = [];
        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Hình Vuông',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        const squareName = this.points.map(p => p.name).join('');

        // Determine which construction method to use
        let constructionSquare: GeoElConstructionRequest;

        if (this.points.length === 4) {
            constructionSquare = this.buildSquareFromPointsConstruction(this.points.map(p => p.name));
        } else if (constructionPoints.length < 2) {
            const lineName = `${this.points[0].name}${this.points[1].name}`;
            constructionSquare = this.buildSquareFromLineSegmentConstruction(squareName, lineName, nth);
        } else {
            constructionSquare = this.buildSquareFromTwoPositionConstruction(
                squareName,
                this.points[0].coords,
                this.points[1].coords,
                nth
            );
        }

        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình vuông',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docGlobalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionSquare,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length == 0 || this.points.length == 4) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
            // while pointer down -> handle all 3 cases
            if (this.points.length === 1) {
                this.handleFirstPoint(event);
            } else if (this.points.length === 2) {
                this.handleSecondPoint(event);
            } else if (this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        } else {
            // other mouse move -> only handle preview for the third point
            if (this.points.length === 2 || this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        }
    }

    private buildSquareFromTwoPositionConstruction(
        squareName: string,
        pos1: number[],
        pos2: number[],
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Square/SquareEC', 'Square', 'FromTwoPosition');
        construction.name = squareName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos1,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos2,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildSquareFromLineSegmentConstruction(
        squareName: string,
        lineName: string,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Square/SquareEC', 'Square', 'FromLineSegment');
        construction.name = squareName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-LineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildSquareFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Square/SquareEC', 'Square', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }

    private async previewSquare(ctrl: GeoDocCtrl, ...faces: number[][]) {
        // const polygon: PreviewPolygon = {
        //     relIndex: -20,
        //     name: '',
        //     type: 'RenderPolygon',
        //     elType: 'Square',
        //     verts: faces,
        //     renderProp: buildPreviewPolygonRenderProp(),
        //     usable: true,
        //     valid: true,
        // };
        // await syncPreviewCommands(polygon, ctrl);
    }
}
