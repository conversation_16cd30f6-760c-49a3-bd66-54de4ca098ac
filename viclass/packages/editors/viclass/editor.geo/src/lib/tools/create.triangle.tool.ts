import { line, point, vector } from '@flatten-js/core';
import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderLineSegment,
    RenderVertex,
    TriangleToolState,
} from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/preview.util';
import { nthSideOfVector } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { repeat, RepeatSelector, SelectedVertex, then, ThenSelector, vert, vertex, vertexS } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, remoteConstruct } from './tool.utils';

export abstract class BaseTriangleTool<T extends CommonToolState> extends GeometryTool<T> {
    protected basePoints: GeoRenderElement[] = [];
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();

    protected abstract createSelLogic();

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();

        this.createSelLogic();
    }

    excludeSelection(el: GeoRenderElement) {
        return !this.basePoints.includes(el);
    }

    override resetState() {
        this.selLogic.reset();
        this.basePoints = [];
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (selected) {
            if (selected.length == 2) {
                // has all data, preview triangle
                const p1 = vert(selected[0][0]);
                const p2 = vert(selected[0][1]);
                const p3 = Array.isArray(selected[1])
                    ? vert(selected[1] as SelectedVertex)
                    : (selected[1] as RenderVertex);

                this.pQ.add(pLine(ctrl, -21, RenderLineSegment, p1, p3));
                this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p2, p3));
            } else if (selected.length == 1 && Array.isArray(selected[0]) && selected[0].length == 2) {
                // preview the base
                this.pQ.add(
                    pLine(
                        ctrl,
                        -20,
                        RenderLineSegment,
                        vert(selected[0][0] as SelectedVertex),
                        vert(selected[0][1] as SelectedVertex)
                    )
                );
            }
        }
        this.pQ.flush(ctrl);
    }

    protected abstract performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl);

    get first2Points(): RepeatSelector<SelectedVertex> {
        const first2Points = vertexS(this.pQ, this.pointerHandler.cursor);
        first2Points.get('vertex').setOption(
            'refinedFilter',
            this.excludeSelection.bind(this) // do not reselect anything has been selected
        );

        return repeat<SelectedVertex>(first2Points, {
            count: 2,
            onPartialSelection: newSel => this.basePoints.push(vert(newSel)) && true,
        });
    }
}

export class CreateTriangleTool extends BaseTriangleTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateTriangleTool';

    protected override createSelLogic() {
        const lastVertex = vertexS(this.pQ, this.pointerHandler.cursor);
        lastVertex.get('vertex').setOption('refinedFilter', this.excludeSelection.bind(this));

        this.selLogic = then([this.first2Points, lastVertex], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    protected async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [
            ...(selector.selected[0] as SelectedVertex[]), // first two point from repeat selector
            selector.selected[1] as SelectedVertex, // last point
        ];
        const { pcs, points } = await assignNames(
            ctrl,
            selected,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Tam giác'
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const triangleName = `${points[0].name}${points[1].name}${points[2].name}`;
        const constructionTriangle = this.buildTriangleConstruction(triangleName);

        try {
            await remoteConstruct(ctrl, constructionTriangle, pcs, this.editor.geoGateway, 'tam giác');
        } finally {
            this.resetState();
        }
    }

    private buildTriangleConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Triangle/TriangleBaseEC', 'Triangle', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }
}
export class CreateRightTriangleTool extends BaseTriangleTool<TriangleToolState> {
    readonly toolType: GeometryToolType = 'CreateRightTriangleTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.registerKeyboardHandling(
            {
                event: 'keydown',
                keys: ['shift'],
                global: false,
            },
            {
                event: 'keyup',
                keys: ['shift'],
                global: false,
            }
        );
    }

    protected override createSelLogic(): void {
        const lastVertex = vertex({
            // then select a vertex
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: this.excludeSelection.bind(this),
            tfunc: this.lastPointProjection.bind(this),
            cfunc: this.checkLastPoint.bind(this),
        });

        this.selLogic = then([this.first2Points, lastVertex], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    lastPointProjection(el: RenderVertex): RenderVertex {
        const p1 = this.basePoints[0] as RenderVertex;
        const p2 = this.basePoints[1] as RenderVertex;
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]);
        const p = point(el.coords[0], el.coords[1]);

        if (!this.toolState.createFromBase) {
            // first two points is one of the legs
            // two perpendicular line to the base
            const l1 = line(fp1, vector(fp1, fp2));
            const l2 = line(fp2, vector(fp1, fp2));
            const prj1 = p.projectionOn(l1);
            const prj2 = p.projectionOn(l2);
            const d1 = p.distanceTo(prj1)[0];
            const d2 = p.distanceTo(prj2)[0];
            const chosen = d1 < d2 ? prj1 : prj2;

            el.coords[0] = chosen.x;
            el.coords[1] = chosen.y;
            return el;
        } else {
            // first two points are hypothenuse
            const midpoint = fp1.translate(vector(fp1, fp2).multiply(0.5));
            const radius = fp1.distanceTo(fp2)[0] / 2;
            const vmid = vector(midpoint, p).normalize().multiply(radius);

            const chosen = midpoint.translate(vmid);

            el.coords[0] = chosen.x;
            el.coords[1] = chosen.y;
            return el;
        }
    }

    checkLastPoint(el: RenderVertex): boolean {
        const p1 = this.basePoints[0] as RenderVertex;
        const p2 = this.basePoints[1] as RenderVertex;
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]);
        const p = point(el.coords[0], el.coords[1]);

        if (!this.toolState.createFromBase) {
            // first two points is one of the legs
            // two perpendicular line to the base
            const l1 = line(fp1, vector(fp1, fp2));
            const l2 = line(fp2, vector(fp1, fp2));
            const prj1 = p.projectionOn(l1);
            const prj2 = p.projectionOn(l2);
            const d1 = p.distanceTo(prj1)[0];
            const d2 = p.distanceTo(prj2)[0];
            const chosen = d1 < d2 ? prj1 : prj2;

            const diff = chosen.distanceTo(p)[0];

            return diff < 0.00001;
        } else {
            // first two points are hypothenuse
            const midpoint = fp1.translate(vector(fp1, fp2).multiply(0.5));
            const radius = fp1.distanceTo(fp2)[0] / 2;
            const diff = Math.abs(p.distanceTo(midpoint)[0] - radius);

            return diff < 0.00001;
        }
    }

    baseAndApex(rv: RenderVertex[], toolChoice: boolean): [number, number, number] {
        // otherwise, determine the suitable base on the points we have
        const ps = rv.map(v => point(v.coords[0], v.coords[1]));
        let apex: number, b1: number, b2: number; // apex, base point 1, base point 2

        // determine the apex, and two base point base on the user input
        if (this.toolState.createFromBase) {
            // if create from base, the 3rd point is the apex
            apex = 2;
            b1 = 0;
            b2 = 1;
        } else {
            const d0 = Math.abs(ps[2].distanceTo(ps[0])[0]);
            const d1 = Math.abs(ps[2].distanceTo(ps[1])[0]);

            if (d0 < d1) {
                // ps[2] distance to point 0 is the side length, so point 0 is the apex
                apex = 0;
                b1 = 1;
                b2 = 2;
            } else {
                apex = 1;
                b1 = 0;
                b2 = 2;
            }
        }

        // if we create from base, and one and only one of the points is known, make sure it is used in the base and the other one is constrained
        // if we create from the side, and one and only one of the points is known, make sure it is used in the side and the other one is constrained
        // hence, we swap b1 and b2 if b2 is known and b1 is not known to ensure b1 is the known point, if both are unknown, the order remains the same
        if (rv[b2].relIndex >= 0 && rv[b1].relIndex < 0) {
            const temp = b2;
            b2 = b1;
            b1 = temp;
        }

        return [apex, b1, b2];
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [
            ...(selector.selected[0] as SelectedVertex[]), // first two point from repeat selector
            selector.selected[1] as SelectedVertex, // last point
        ];
        // The list of render vertices in order of clicking
        const rv = selected.map(v => vert(v));
        const ps = rv.map(v => point(v.coords[0], v.coords[1]));
        const [apex, b1, b2] = this.baseAndApex(rv, this.toolState.createFromBase);

        /**
         * When do we need to create from 3 points?
         *
         * When ALL THREE POINTS are existing points, then we must create this triangle from 3 points, because they cannot
         * be established by any other rules. If one of the point is unknown, then we can use either of this constructions:
         *
         * - base and height
         * - side angle and hypothenuse
         *
         * To establish the position of the unknown point.
         *
         */
        const need3PointsConstruct = rv.every(p => p.name);
        let constructionTriangle: GeoElConstructionRequest;
        // constructions for possible unknown points
        const constructionPoints: GeoElConstructionRequest[] = [];

        if (need3PointsConstruct) {
            // if we need to construct 3 points, do it directly
            constructionTriangle = this.from3Points(
                rv.map(p => p.name),
                rv[apex].name
            );
        } else {
            const isBase = isBaseConstruct(rv[b1], rv[b2], rv[apex], this.toolState.createFromBase);
            const { pcs } = await assignNameAndCreatePointConstructions(
                ctrl,
                selected[b1],
                selected[b2],
                selected[apex],
                isBase,
                this.toolbar.getTool('NamingElementTool') as NamingElementTool
            );

            constructionPoints.push(...pcs);

            if (isBase) {
                const nth = nthSideOfVector(ps, b1, b2, apex, rv[b1].relIndex, rv[b2].relIndex);
                const sideAngle = (Math.abs(vector(ps[b1], ps[apex]).angleTo(vector(ps[b1], ps[b2]))) / Math.PI) * 180;
                constructionTriangle = this.fromBaseNSideAngleConstruction(
                    `${rv[apex].name}${rv[b1].name}${rv[b2].name}`,
                    `${rv[b1].name}${rv[b2].name}`,
                    sideAngle,
                    rv[b1].name,
                    nth
                );
            } else {
                const nth = nthSideOfVector(ps, apex, b1, b2, rv[apex].relIndex, rv[b1].relIndex);
                const length = ps[b2].distanceTo(line(ps[apex], ps[b1]))[0];
                constructionTriangle = this.fromSideNSideLengthConstruction(
                    `${rv[apex].name}${rv[b1].name}${rv[b2].name}`,
                    `${rv[apex].name}${rv[b1].name}`, // side 1
                    `${rv[apex].name}${rv[b2].name}`, // side 2
                    length,
                    nth
                );
            }
        }

        try {
            await remoteConstruct(
                ctrl,
                constructionTriangle,
                constructionPoints,
                this.editor.geoGateway,
                'tam giác vuông'
            );
        } finally {
            this.resetState();
        }
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.createFromBase = !this.toolState.createFromBase;
            this.toolbar.update(this.toolType, this.toolState);
        }

        return event;
    }

    private fromBaseNSideAngleConstruction(
        triangleName: string,
        lineName: string,
        sideAngle: number,
        anglePointName: string,
        nth: number
    ): GeoElConstructionRequest {
        if (sideAngle > 180) sideAngle = 360 - sideAngle;
        const construction = new GeoElConstructionRequest(
            'RightTriangle/RightTriangleEC',
            'RightTriangle',
            'HypotenuseAndAdjacentAngle'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-Hypotenuse',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'nameWithValue',
                optional: true,
                tplStrLangId: 'tpl-AdjacentAngle',
                params: {
                    value: {
                        type: 'singleValue',
                        value: sideAngle,
                    },
                    name: {
                        type: 'singleValue',
                        value: anglePointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private fromSideNSideLengthConstruction(
        triangleName: string,
        side1: string,
        side2: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RightTriangle/RightTriangleEC',
            'RightTriangle',
            'AdjacentSideAndAdjacentSideLength'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-AdjacentSide',
                params: {
                    name: {
                        type: 'singleValue',
                        value: side1,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'lengthAssignment',
                optional: false,
                tplStrLangId: 'tpl-AdjacentSideLength',
                params: {
                    name: {
                        type: 'singleValue',
                        value: side2,
                    },
                    expression: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private from3Points(pointNames: string[], apexPointName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RightTriangle/RightTriangleEC',
            'RightTriangle',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-AtPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: apexPointName,
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateIsoscelesTriangleTool extends BaseTriangleTool<TriangleToolState> {
    readonly toolType: GeometryToolType = 'CreateRightTriangleTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.registerKeyboardHandling(
            {
                event: 'keydown',
                keys: ['shift'],
                global: false,
            },
            {
                event: 'keyup',
                keys: ['shift'],
                global: false,
            }
        );
    }

    protected override createSelLogic(): void {
        const lastVertex = vertex({
            // then select a vertex
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
            refinedFilter: this.excludeSelection.bind(this),
            tfunc: this.lastPointProjection.bind(this),
            cfunc: this.checkLastPoint.bind(this),
        });

        this.selLogic = then([this.first2Points, lastVertex], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    lastPointProjection(el: RenderVertex): RenderVertex {
        const p1 = this.basePoints[0] as RenderVertex;
        const p2 = this.basePoints[1] as RenderVertex;
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]);
        const p = point(el.coords[0], el.coords[1]);

        if (this.toolState.createFromBase) {
            // first two points is one of the legs
            // two perpendicular line to the base
            const midpoint = fp1.translate(vector(fp1, fp2).multiply(0.5));
            const l1 = line(midpoint, vector(fp1, fp2).normalize());

            const chosen = p.projectionOn(l1);

            el.coords[0] = chosen.x;
            el.coords[1] = chosen.y;
            return el;
        } else {
            // first two points are hypothenuse
            const radius = fp1.distanceTo(fp2)[0];
            const d1 = p.distanceTo(fp1)[0];
            const d2 = p.distanceTo(fp2)[0];
            const center = d2 < d1 ? fp1 : fp2;
            const v = vector(center, p).normalize().multiply(radius);
            const chosen = center.translate(v);

            el.coords[0] = chosen.x;
            el.coords[1] = chosen.y;
            return el;
        }
    }

    checkLastPoint(el: RenderVertex): boolean {
        const p1 = this.basePoints[0] as RenderVertex;
        const p2 = this.basePoints[1] as RenderVertex;
        const fp1 = point(p1.coords[0], p1.coords[1]);
        const fp2 = point(p2.coords[0], p2.coords[1]);
        const p = point(el.coords[0], el.coords[1]);

        if (this.toolState.createFromBase) {
            // first two points is one of the legs
            // two perpendicular line to the base
            const diff = Math.abs(fp1.distanceTo(p)[0] - fp2.distanceTo(p)[0]);

            return diff < 0.00001;
        } else {
            // first two points are hypothenuse
            const radius = fp1.distanceTo(fp2)[0];
            const d1 = p.distanceTo(fp1)[0];
            const d2 = p.distanceTo(fp2)[0];
            const center = d2 < d1 ? fp1 : fp2;
            const diff = Math.abs(p.distanceTo(center)[0] - radius);

            return diff < 0.00001;
        }
    }

    private baseAndApex(rv: RenderVertex[], toolChoice: boolean): [number, number, number] {
        const ps = rv.map(v => point(v.coords[0], v.coords[1]));
        let apex: number, b1: number, b2: number; // apex, base point 1, base point 2

        // determine the apex, and two base point base on the user input
        if (toolChoice) {
            // if create from base, the 3rd point is the apex
            apex = 2;
            b1 = 0;
            b2 = 1;
        } else {
            // deternmin the apex by checking distance of the point 3 to the other two points
            const sideLength = ps[0].distanceTo(ps[1])[0];
            const diff1 = Math.abs(ps[2].distanceTo(ps[0])[0] - sideLength);
            const diff2 = Math.abs(ps[2].distanceTo(ps[1])[0] - sideLength);

            if (diff1 < diff2) {
                // ps[2] distance to point 0 is the side length, so point 0 is the apex
                apex = 0;
                b1 = 1;
                b2 = 2;
            } else {
                apex = 1;
                b1 = 0;
                b2 = 2;
            }
        }

        // if we create from base, and one and only one of the points is known, make sure it is used in the base and the other one is constrained
        // if we create from the side, and one and only one of the points is known, make sure it is used in the side and the other one is constrained
        // hence, we swap b1 and b2 if b2 is known and b1 is not known to ensure b1 is the known point, if both are unknown, the order remains the same
        if (rv[b2].relIndex >= 0 && rv[b1].relIndex < 0) {
            const temp = b2;
            b2 = b1;
            b1 = temp;
        }

        return [apex, b1, b2];
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [
            ...(selector.selected[0] as SelectedVertex[]), // first two point from repeat selector
            selector.selected[1] as SelectedVertex, // last point
        ];

        // The list of render vertices in order of clicking
        const rv = selected.map(v => vert(v));
        const ps = rv.map(v => point(v.coords[0], v.coords[1]));
        const [apex, b1, b2] = this.baseAndApex(rv, this.toolState.createFromBase);

        /**
         * When do we need to create from 3 points?
         *
         * When ALL THREE POINTS are existing points, then we must create this triangle from 3 points, because they cannot
         * be established by any other rules. If one of the point is unknown, then we can use either of this constructions:
         *
         * - base and height
         * - side and apex angle
         *
         * To establish the position of the unknown point.
         *
         */
        const need3PointsConstruct = rv.every(p => p.name);
        let constructionTriangle: GeoElConstructionRequest;
        // constructions for possible unknown points

        const constructionPoints: GeoElConstructionRequest[] = [];

        if (need3PointsConstruct) {
            // if we need to construct 3 points, do it directly
            constructionTriangle = this.from3Points(
                rv.map(p => p.name),
                rv[apex].name
            );
        } else {
            // otherwise, determine the suitable base on the points we have

            const isBase = isBaseConstruct(rv[b1], rv[b2], rv[apex], this.toolState.createFromBase);

            const { pcs } = await assignNameAndCreatePointConstructions(
                ctrl,
                selected[b1],
                selected[b2],
                selected[apex],
                isBase,
                this.toolbar.getTool('NamingElementTool') as NamingElementTool
            );

            constructionPoints.push(...pcs);

            if (isBase) {
                const nth = nthSideOfVector(ps, b1, b2, apex, rv[b1].relIndex, rv[b2].relIndex);
                const height = vector(ps[apex], ps[b1]).add(vector(ps[b1], ps[b2]).multiply(0.5)).length;

                constructionTriangle = this.fromBaseNHeightConstruction(
                    `${rv[b1].name}${rv[b2].name}${rv[apex].name}`,
                    `${rv[b1].name}${rv[b2].name}`,
                    height,
                    nth
                );
            } else {
                const nth = nthSideOfVector(ps, apex, b1, b2, rv[apex].relIndex, rv[b1].relIndex);
                const angle = (Math.abs(vector(ps[apex], ps[b1]).angleTo(vector(ps[apex], ps[b2]))) / Math.PI) * 180;
                constructionTriangle = this.fromSideNApexAngle(
                    `${rv[apex].name}${rv[b1].name}${rv[b2].name}`,
                    `${rv[apex].name}${rv[b1].name}`,
                    angle,
                    rv[apex].name,
                    nth
                );
            }
        }

        try {
            await remoteConstruct(
                ctrl,
                constructionTriangle,
                constructionPoints,
                this.editor.geoGateway,
                'tam giác cân'
            );
        } finally {
            this.resetState();
        }
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.createFromBase = !this.toolState.createFromBase;
            this.toolbar.update('CreateIsoscelesTriangleTool', this.toolState);
        }

        return event;
    }

    private fromBaseNHeightConstruction(
        triangleName: string,
        lineName: string,
        height: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'BaseLineSegmentAndHeightValue'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-BaseSideIs',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-HeightValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: height,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private fromSideNApexAngle(
        triangleName: string,
        lineName: string,
        apexAngle: number,
        apexName: string,
        nth: number
    ): GeoElConstructionRequest {
        if (apexAngle > 180) apexAngle = 360 - apexAngle;
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'SideLineSegmentApexAngle'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-SideIs',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'nameWithValue',
                optional: true,
                tplStrLangId: 'tpl-ApexAngle',
                params: {
                    value: {
                        type: 'singleValue',
                        value: apexAngle,
                    },
                    name: {
                        type: 'singleValue',
                        value: apexName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private from3Points(pointNames: string[], apexPointName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-AtPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: apexPointName,
                    },
                },
            },
        ];

        return construction;
    }
}

function isBaseConstruct(b1: RenderVertex, b2: RenderVertex, apex: RenderVertex, userChooseBase: boolean) {
    const knownBasePoints = [b1, b2].filter(p => p.relIndex >= 0).length;

    // if both base points are existing points, there is no way but to create from base
    // if one of the base point is not known, and apex IS KNOWN, we must create from side
    // otherwise, we create from user instruction
    let isBase = userChooseBase;
    if (knownBasePoints == 2) isBase = true;
    else if (apex.relIndex >= 0) isBase = false;
    return isBase;
}

async function assignNameAndCreatePointConstructions(
    ctrl: GeoDocCtrl,
    b1: SelectedVertex,
    b2: SelectedVertex,
    apex: SelectedVertex,
    isBase: boolean,
    namingTool: NamingElementTool
): Promise<{ pcs: GeoElConstructionRequest[]; points: RenderVertex[] }> {
    const { pcs, points } = await assignNames(ctrl, [apex, b1, b2], namingTool, 'Tam giac'); // we assign names for all points

    if (!pcs) return { pcs: undefined, points: undefined };

    const pointsNeedConstructions = isBase ? [vert(b1).name, vert(b2).name] : [vert(apex).name, vert(b1).name];

    // need to filter out all points that needs not construct
    return { pcs: pcs.filter(c => pointsNeedConstructions.includes(c.name)), points: points };
}
