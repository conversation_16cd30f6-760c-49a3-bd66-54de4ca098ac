import {
    CRDTool,
    newCursor,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerType<PERSON>en,
    pointerTypePenMouse,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeometryTool } from './geo.tool';

export class GeoCreateDocumentTool extends GeometryTool<any> {
    readonly toolType: GeometryToolType = 'CreateDocumentTool';

    override readonly toolCursor = [newCursor('crosshair', 0, '', 0, 'system')];

    private delegator: CRDTool;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.registerPointerHandling(
            { event: 'pointerdown', button: 0, pointerTypes: pointerType<PERSON>enMouse },
            { event: 'pointerup', button: 0, pointerTypes: pointerType<PERSON>enMouse },
            { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', pointerTypes: pointerTypePen, numPointer: 1 },

            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 }
        );
    }

    isCreatingDoc$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    override resetState() {
        this.delegator.resetState();
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.nativeEvent.key == 'Escape') {
            setTimeout(() => this.toolbar.blur(this.toolType));
        }

        return this.delegator.handleKeyboardEvent(event);
    }

    override onDetachViewport() {
        this.delegator.destroy(this.toolbar.viewport);
    }

    override onAttachViewport() {
        this.delegator = this.toolbar.editor.initCreateDocTool(this.toolbar.viewport);
        this.delegator.toolbar = this.toolbar;
        this.delegator.isCreatingDoc$ = this.isCreatingDoc$;
    }

    override onBlur(): void {
        super.onBlur();
        this.delegator?.resetState();
    }

    override onDisable(): void {
        this.delegator?.resetState();
    }

    override onFocus(): void {
        super.onFocus();
        this.delegator?.onFocusCreateDocTool();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        return this.delegator.handlePointerEvent(event);
    }
}
