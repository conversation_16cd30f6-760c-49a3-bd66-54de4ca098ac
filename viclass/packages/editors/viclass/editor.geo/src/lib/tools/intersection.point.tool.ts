import { arc, circle, line, point, vector } from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { AxiosError } from 'axios';
import { syncEndPreviewModeCommand, syncPreviewCommands, syncRenderCommands } from '../cmd';
import {
    intersectionCircleEllipse,
    intersectionEllipses,
    intersectionLineEllipse,
} from '../element.intersection/intersections';
import { geoDefaultHandlerFn, GeoErr } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    ParamSpecs,
    RenderAngle,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionOnLine, nthDirectionRotation, sortByRotationV2 } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewVertexRenderProp,
    createVector,
    isElementLine,
    isValidIdx,
    pickPointName,
    requestElementNames,
} from './tool.utils';

type IntersectionElementDetails = {
    name: string; // Name of the geometric element (e.g., "l1", "c1")
    elType?: string; // Specific type like "LineSegment" used in params if exists
    labelType: string; // Generic type for tpl: "Line", "Circle", "Ellipse", "Sector"
    defId: string; // ParamDefId for this element type: "aLine", "aCircle", etc.
};

type IntersectionRequestArgs = {
    cgName: string; // From Kotlin CGS enum, e.g., "LineLine"
    outputName: string;
    paramA: IntersectionElementDetails;
    paramB: IntersectionElementDetails;
    nth?: number; // 1-based index if present
};

function getElementDetails(el: RenderLine | RenderCircle | RenderEllipse | RenderSector): IntersectionElementDetails {
    if (isElementLine(el)) {
        const defId = (() => {
            switch (el.elType) {
                case 'Ray':
                    return 'aRay';
                case 'LineSegment':
                    return 'aLineSegment';
                case 'VectorVi':
                    return 'aVector';
                default:
                    return 'aLine';
            }
        })();
        return { name: el.name, elType: el.elType, labelType: 'Line', defId };
    } else if (el.type === 'RenderCircle') {
        return { name: el.name, elType: el.elType, labelType: 'Circle', defId: 'aCircle' };
    } else if (el.type === 'RenderEllipse') {
        return { name: el.name, elType: el.elType, labelType: 'Ellipse', defId: 'anEllipse' };
    } else if (el.type === 'RenderSector') {
        return { name: el.name, elType: el.elType, labelType: 'Sector', defId: 'aCircularSector' };
    }
    // Should not happen due to filtering
    throw new Error(`Unknown element type for intersection: ${el.type}`);
}

function buildIntersectionRequest(args: IntersectionRequestArgs): GeoElConstructionRequest {
    const construction = new GeoElConstructionRequest(
        'Point/IntersectionPointEC', // Matches Kotlin class name
        'Point', // Output element type
        args.cgName,
        args.outputName
    );

    const paramSpecs: ParamSpecs[] = [
        {
            paramDefId: args.paramA.defId,
            indexInCG: 0,
            optional: false,
            tplStrLangId: `tpl-IntersectionOf${args.paramA.labelType}`,
            params: {
                name: { type: 'singleValue', value: args.paramA.name },
            },
        },
        {
            paramDefId: args.paramB.defId,
            indexInCG: 1,
            optional: false,
            tplStrLangId: `tpl-IntersectionWith${args.paramB.labelType}`,
            params: {
                name: { type: 'singleValue', value: args.paramB.name },
            },
        },
    ];

    if (args.nth !== undefined) {
        paramSpecs.push({
            paramDefId: 'aValue', // nth parameter is always 'aValue'
            indexInCG: 2,
            optional: true, // nth is conceptually optional
            tplStrLangId: 'tpl-thIntersection',
            params: { value: { type: 'singleValue', value: args.nth.toString() } },
        });
    }
    construction.paramSpecs = paramSpecs;
    return construction;
}

export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    override readonly toolType: GeometryToolType = 'IntersectionPointTool';

    private points: RenderVertex[] = [];
    private lines: RenderLine[] = [];
    private circles: RenderCircle[] = [];
    private sectors: RenderSector[] = [];
    private ellipses: RenderEllipse[] = [];
    private angles: RenderAngle[] = [];
    private intersectionPreview: RenderVertex[] = [];
    private intersectionConstructed: RenderVertex[] = [];
    private isPointerDown = false;

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return (
            isElementLine(el) ||
            el.type == 'RenderVertex' ||
            el.type == 'RenderSector' ||
            el.type == 'RenderCircle' ||
            el.type == 'RenderEllipse'
        );
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start selection
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm selection
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 }
        );
    }

    override resetState() {
        this.points = [];
        this.lines = [];
        this.circles = [];
        this.ellipses = [];
        this.sectors = [];
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        const { ctrl, hitCtx, hitEl } = this.posAndCtrl(event);
        if (!hitEl) {
            syncEndPreviewModeCommand(ctrl);
            this.resetState();
            return;
        }

        ctrl.editor.selectElement(hitCtx, true);
        await this.processSelectObject(ctrl, hitEl);
    }

    private async processSelectObject(docCtrl: GeoDocCtrl, el: GeoRenderElement) {
        // check and process build selected intersection construction
        if (this.intersectionPreview.length > 0) {
            if (this.intersectionPreview.some(e => e.relIndex == el.relIndex)) {
                const intersectionSelected = el as RenderVertex;
                await this.processBuildConstruction(docCtrl, intersectionSelected);
                return;
            }

            syncEndPreviewModeCommand(docCtrl);
            this.resetState();
            return;
        }

        if (el.type === 'RenderVertex') {
            if (this.points.length == 1 && this.points[0].relIndex == el.relIndex) return;

            this.points.push(el as RenderVertex);

            if (this.points.length < 2) return;

            let [p1, p2] = [this.points.shift()!, this.points.shift()!];

            // check point order and swap if wrong order compared to renderEl
            const previewVector = [p2.coords[0] - p1.coords[0], p2.coords[1] - p1.coords[1]];
            const calculationVector = createVector(p1, p2);
            if (calculationVector[0] != previewVector[0] && calculationVector[1] != previewVector[1]) {
                [p1, p2] = [p2, p1];
            }

            this.points = [];

            // const line: RenderLine = {
            //     renderProp: undefined,
            //     type: 'RenderLineSegment',
            //     elType: 'LineSegment',
            //     name: `${p1.name}${p2.name}`,
            //     usable: true,
            //     valid: true,
            //     relIndex: undefined,
            //     startPointIdx: p1.relIndex,
            //     endPointIdx: p2.relIndex,
            //     vector: [p2.coords[0] - p1.coords[0], p2.coords[1] - p1.coords[1]],
            // };

            // this.lines.push(line);
        } else if (isElementLine(el)) {
            this.points = [];
            const line = el as RenderLine;
            if (this.lines.length == 1 && this.lines[0].relIndex == el.relIndex) return;
            this.lines.push(line);
        } else if (el.type === 'RenderSector') {
            this.points = [];
            const sector = el as RenderSector;
            if (isValidIdx(sector.startPointIdx) && isValidIdx(sector.endPointIdx)) {
                if (this.sectors.length == 1 && this.sectors[0].relIndex == el.relIndex) return;
                this.sectors.push(sector);
            }
        } else if (el.type === 'RenderEllipse') {
            this.points = [];
            const ellipse = el as RenderEllipse;
            if (this.ellipses.length == 1 && this.ellipses[0].relIndex == el.relIndex) return;
            this.ellipses.push(ellipse);
        } else if (el.type === 'RenderCircle') {
            this.points = [];
            const circle = el as RenderCircle;
            if (this.circles.length == 1 && this.circles[0].relIndex == el.relIndex) return;
            this.circles.push(circle);
        } else if (el.type === 'RenderAngle') {
            this.points = [];
            const angle = el as RenderAngle;
            if (this.angles.length == 1 && this.angles[0].relIndex == el.relIndex) return;
            this.angles.push(angle);
        }

        await this.processPreviewIntersectionPoint(docCtrl);
    }

    // Các trường hợp ghép cặp giữa line, circle, ellipse, sector (ngắn gọn)
    private async processPreviewIntersectionPoint(docCtrl: GeoDocCtrl) {
        if (this.intersectionPreview.length > 1) return;
        if (this.lines.length === 2) {
            await this.previewLineLineIntersection(docCtrl);
            return;
        }
        if (this.lines.length === 1 && this.circles.length === 1) {
            await this.previewLineCircleIntersection(docCtrl);
            return;
        }
        if (this.lines.length === 1 && this.ellipses.length === 1) {
            await this.previewLineEllipseIntersection(docCtrl);
            return;
        }
        if (this.lines.length === 1 && this.sectors.length === 1) {
            await this.previewLineSectorIntersection(docCtrl);
            return;
        }
        if (this.circles.length === 2) {
            await this.previewCircleCircleIntersection(docCtrl);
            return;
        }
        if (this.circles.length === 1 && this.ellipses.length === 1) {
            await this.previewCircleEllipseIntersection(docCtrl);
            return;
        }
        if (this.circles.length === 1 && this.sectors.length === 1) {
            await this.previewCircleSectorIntersection(docCtrl);
            return;
        }
        if (this.ellipses.length === 2) {
            await this.previewEllipseEllipseIntersection(docCtrl);
            return;
        }
        if (this.ellipses.length === 1 && this.sectors.length === 1) {
            await this.previewEllipseSectorIntersection(docCtrl);
            return;
        }
        if (this.sectors.length === 2) {
            await this.previewSectorSectorIntersection(docCtrl);
            return;
        }
    }

    // --- Helper methods for preview intersection points ---

    private createPreviewVertex(coords: [number, number], relIndex: number): RenderVertex {
        return {
            relIndex,
            type: 'RenderVertex',
            elType: 'Point',
            coords,
            renderProp: buildPreviewVertexRenderProp(),
            usable: true,
            valid: true,
            name: undefined,
            unselectable: false,
        };
    }

    private async previewLineLineIntersection(docCtrl: GeoDocCtrl) {
        const [selectedLine1, selectedLine2] = this.lines.sort((a, b) => a.relIndex - b.relIndex);
        const startPointLine1 = docCtrl.rendererCtrl.elementAt(selectedLine1.startPointIdx) as RenderVertex;
        const startPointLine2 = docCtrl.rendererCtrl.elementAt(selectedLine2.startPointIdx) as RenderVertex;

        const l1 = line(
            point(startPointLine1.coords[0], startPointLine1.coords[1]),
            vector(-selectedLine1.vector[1], selectedLine1.vector[0])
        );
        const l2 = line(
            point(startPointLine2.coords[0], startPointLine2.coords[1]),
            vector(-selectedLine2.vector[1], selectedLine2.vector[0])
        );

        const intersections = l1.intersect(l2);

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        // Without isIntersectionPointOnLine check; all intersections from intersect() of two lines are valid
        const pvP = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
        this.intersectionPreview.push(pvP);

        await this.processBuildConstruction(docCtrl, pvP);
        // resetState is now handled inside processBuildConstruction's finally block
    }

    private async previewLineCircleIntersection(docCtrl: GeoDocCtrl) {
        const [selectedLine] = this.lines;
        const [selectedCircle] = this.circles;
        const layerRenderer = docCtrl.rendererCtrl;

        const centerPoint = layerRenderer.elementAt(selectedCircle.centerPointIdx) as RenderVertex;
        const startPoint = layerRenderer.elementAt(selectedLine.startPointIdx) as RenderVertex;

        const c = circle(point(centerPoint.coords[0], centerPoint.coords[1]), selectedCircle.radius);
        const l = line(
            point(startPoint.coords[0], startPoint.coords[1]),
            vector(-selectedLine.vector[1], selectedLine.vector[0])
        );

        const intersections = l.intersect(c);

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        // All intersections returned by intersect are valid; no filtering with isIntersectionPointOnLine
        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (nthDirectionOnLine(selectedLine.vector, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    private async previewLineEllipseIntersection(docCtrl: GeoDocCtrl) {
        const [selectedLine] = this.lines;
        const [selectedEllipse] = this.ellipses;
        const layerRenderer = docCtrl.rendererCtrl;

        const f1El = layerRenderer.elementAt(selectedEllipse.f1Idx) as RenderVertex;
        const f2El = layerRenderer.elementAt(selectedEllipse.f2Idx) as RenderVertex;
        const startPoint = layerRenderer.elementAt(selectedLine.startPointIdx) as RenderVertex;

        const l = line(
            point(startPoint.coords[0], startPoint.coords[1]),
            vector(-selectedLine.vector[1], selectedLine.vector[0])
        );

        const pF1 = point(f1El.coords[0], f1El.coords[1]);
        const pF2 = point(f2El.coords[0], f2El.coords[1]);
        const pC = point((pF1.x + pF2.x) / 2, (pF1.y + pF2.y) / 2);

        const intersections = intersectionLineEllipse(
            l,
            pC,
            selectedEllipse.a,
            selectedEllipse.b,
            selectedEllipse.rotate
        );

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        // No filtering with isIntersectionPointOnLine
        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (nthDirectionOnLine(selectedLine.vector, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    private async previewLineSectorIntersection(docCtrl: GeoDocCtrl) {
        const [selectedLine] = this.lines;
        const [selectedSector] = this.sectors;
        const layerRenderer = docCtrl.rendererCtrl;

        const center1 = docCtrl.rendererCtrl.elementAt(selectedSector.centerPointIdx) as RenderVertex;
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.endPointIdx) as RenderVertex;
        const pC1 = point(center1.coords[0], center1.coords[1]);
        const pS1 = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1 = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1O = vector(pC1, point(pC1.x + 10, pC1.y));
        const angleStart1 = vecC1O.angleTo(vector(pC1, pS1));
        const angleEnd1 = vecC1O.angleTo(vector(pC1, pE1));

        const lineStartPoint = layerRenderer.elementAt(selectedLine.startPointIdx) as RenderVertex;

        const curve = arc(pC1, selectedSector.radius, angleStart1, angleEnd1);
        const l = line(
            point(lineStartPoint.coords[0], lineStartPoint.coords[1]),
            vector(-selectedLine.vector[1], selectedLine.vector[0])
        );

        const intersections = l.intersect(curve);

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        // No filtering with isIntersectionPointOnLine
        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (nthDirectionOnLine(selectedLine.vector, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    private async previewCircleCircleIntersection(docCtrl: GeoDocCtrl) {
        const [selectedCircle1, selectedCircle2] = this.circles.sort((a, b) => a.relIndex - b.relIndex);
        const p1 = docCtrl.rendererCtrl.elementAt(selectedCircle1.centerPointIdx) as RenderVertex;
        const p2 = docCtrl.rendererCtrl.elementAt(selectedCircle2.centerPointIdx) as RenderVertex;
        const c1 = circle(point(p1.coords[0], p1.coords[1]), selectedCircle1.radius);
        const c2 = circle(point(p2.coords[0], p2.coords[1]), selectedCircle2.radius);

        const intersections = c1.intersect(c2);

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (
                nthDirectionRotation(
                    [p2.coords[0] - p1.coords[0], p2.coords[1] - p1.coords[1]],
                    p1.coords,
                    pvP1.coords,
                    [pvP1.coords, pvP2.coords]
                ) === 1
            ) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    private async previewCircleEllipseIntersection(docCtrl: GeoDocCtrl) {
        const [selectedCircle] = this.circles;
        const [selectedEllipse] = this.ellipses;
        const layerRenderer = docCtrl.rendererCtrl;

        const centerCircle = layerRenderer.elementAt(selectedCircle.centerPointIdx) as RenderVertex;
        const pCCircle = point(centerCircle.coords[0], centerCircle.coords[1]);

        const f1El = layerRenderer.elementAt(selectedEllipse.f1Idx) as RenderVertex;
        const f2El = layerRenderer.elementAt(selectedEllipse.f2Idx) as RenderVertex;
        const pF1 = point(f1El.coords[0], f1El.coords[1]);
        const pF2 = point(f2El.coords[0], f2El.coords[1]);
        const pCEll = point((pF1.x + pF2.x) / 2, (pF1.y + pF2.y) / 2);

        const intersections = intersectionCircleEllipse(
            pCCircle,
            selectedCircle.radius,
            pCEll,
            selectedEllipse.a,
            selectedEllipse.b,
            selectedEllipse.rotate
        );

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        const sortedIntersections = sortByRotationV2(vector(pCEll, pCCircle), pCEll, intersections);

        if (sortedIntersections.length > 1) {
            let i = 0;
            const pvPs = sortedIntersections.map(ints => this.createPreviewVertex([ints.x, ints.y], -9999 - i++));

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                pvPs.find(p => p.relIndex === el.relIndex) != null;

            pvPs.forEach(p => syncPreviewCommands(p, docCtrl));
            this.intersectionPreview.push(...pvPs);
        }
    }

    private async previewCircleSectorIntersection(docCtrl: GeoDocCtrl) {
        const [selectedCircle] = this.circles;
        const [selectedSector] = this.sectors;
        const layerRenderer = docCtrl.rendererCtrl;

        const centerSector = docCtrl.rendererCtrl.elementAt(selectedSector.centerPointIdx) as RenderVertex;
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.endPointIdx) as RenderVertex;
        const pC1Sector = point(centerSector.coords[0], centerSector.coords[1]);
        const pS1Sector = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1Sector = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1OSector = vector(pC1Sector, point(pC1Sector.x + 10, pC1Sector.y));
        const angleStart1 = vecC1OSector.angleTo(vector(pC1Sector, pS1Sector));
        const angleEnd1 = vecC1OSector.angleTo(vector(pC1Sector, pE1Sector));

        const centerCircle = layerRenderer.elementAt(selectedCircle.centerPointIdx) as RenderVertex;
        const pCCircle = point(centerCircle.coords[0], centerCircle.coords[1]);

        const curveSector = arc(pC1Sector, selectedSector.radius, angleStart1, angleEnd1);
        const circle1 = circle(pCCircle, selectedCircle.radius);

        const intersections = circle1.intersect(curveSector);

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            const vec = vector(pCCircle, pC1Sector);

            if (
                nthDirectionRotation([vec.x, vec.y], centerCircle.coords, pvP1.coords, [pvP1.coords, pvP2.coords]) === 1
            ) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    private async previewEllipseEllipseIntersection(docCtrl: GeoDocCtrl) {
        const [selectedEllipse1, selectedEllipse2] = this.ellipses.sort((a, b) => a.relIndex - b.relIndex);
        const f1Ell1 = docCtrl.rendererCtrl.elementAt(selectedEllipse1.f1Idx) as RenderVertex;
        const f2Ell1 = docCtrl.rendererCtrl.elementAt(selectedEllipse1.f2Idx) as RenderVertex;
        const pcEll1 = point((f1Ell1.coords[0] + f2Ell1.coords[0]) / 2, (f1Ell1.coords[1] + f2Ell1.coords[1]) / 2);
        const aEll1 = selectedEllipse1.a;
        const bEll1 = selectedEllipse1.b;
        const rotateEll1 = selectedEllipse1.rotate;

        const f1Ell2 = docCtrl.rendererCtrl.elementAt(selectedEllipse2.f1Idx) as RenderVertex;
        const f2Ell2 = docCtrl.rendererCtrl.elementAt(selectedEllipse2.f2Idx) as RenderVertex;
        const pcEll2 = point((f1Ell2.coords[0] + f2Ell2.coords[0]) / 2, (f1Ell2.coords[1] + f2Ell2.coords[1]) / 2);
        const aEll2 = selectedEllipse2.a;
        const bEll2 = selectedEllipse2.b;
        const rotateEll2 = selectedEllipse2.rotate;

        const intersections = intersectionEllipses(pcEll1, aEll1, bEll1, rotateEll1, pcEll2, aEll2, bEll2, rotateEll2);

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        const sortedIntersections = sortByRotationV2(vector(pcEll1, pcEll2), pcEll1, intersections);

        if (sortedIntersections.length > 1) {
            let i = 0;
            const pvPs = sortedIntersections.map(ints => this.createPreviewVertex([ints.x, ints.y], -9999 - i++));

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                pvPs.find(p => p.relIndex === el.relIndex) != null;

            pvPs.forEach(p => syncPreviewCommands(p, docCtrl));
            this.intersectionPreview.push(...pvPs);
        }
    }

    private async previewEllipseSectorIntersection(docCtrl: GeoDocCtrl) {
        const [selectedEllipse] = this.ellipses;
        const [selectedSector] = this.sectors;
        const layerRenderer = docCtrl.rendererCtrl;

        const centerSector = layerRenderer.elementAt(selectedSector.centerPointIdx) as RenderVertex;
        const pCSector = point(centerSector.coords[0], centerSector.coords[1]);

        const f1El = layerRenderer.elementAt(selectedEllipse.f1Idx) as RenderVertex;
        const f2El = layerRenderer.elementAt(selectedEllipse.f2Idx) as RenderVertex;
        const pF1 = point(f1El.coords[0], f1El.coords[1]);
        const pF2 = point(f2El.coords[0], f2El.coords[1]);
        const pCEll = point((pF1.x + pF2.x) / 2, (pF1.y + pF2.y) / 2);

        // Intersection with the full ellipse
        const intersectionsStep1 = intersectionCircleEllipse(
            pCSector,
            selectedSector.radius,
            pCEll,
            selectedEllipse.a,
            selectedEllipse.b,
            selectedEllipse.rotate
        );

        if (!intersectionsStep1?.length) {
            this.resetState();
            return;
        }

        // Now filter these points to be within the sector's arc
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector.endPointIdx) as RenderVertex;
        const pC1 = point(centerSector.coords[0], centerSector.coords[1]);
        const pS1 = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1 = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1O = vector(pC1, point(pC1.x + 10, pC1.y));
        const angleStart1 = vecC1O.angleTo(vector(pC1, pS1));
        const angleEnd1 = vecC1O.angleTo(vector(pC1, pE1));
        const curveSector = arc(pCSector, selectedSector.radius, angleStart1, angleEnd1);

        const validIntersections = intersectionsStep1.filter(i => curveSector.contains(i));

        if (!validIntersections.length) {
            this.resetState();
            return;
        }

        if (validIntersections.length === 1) {
            const pvP1 = this.createPreviewVertex([validIntersections[0].x, validIntersections[0].y], -9998);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        const sortedValidIntersections = sortByRotationV2(vector(pCEll, pCSector), pCEll, validIntersections);

        if (sortedValidIntersections.length > 1) {
            let i = 0;
            const pvPs = sortedValidIntersections.map(ints => this.createPreviewVertex([ints.x, ints.y], -9999 - i++));

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                pvPs.find(p => p.relIndex === el.relIndex) != null;

            pvPs.forEach(p => syncPreviewCommands(p, docCtrl));
            this.intersectionPreview.push(...pvPs);
        }
    }

    private async previewSectorSectorIntersection(docCtrl: GeoDocCtrl) {
        const [selectedSector1, selectedSector2] = this.sectors.sort((a, b) => a.relIndex - b.relIndex);
        const center1 = docCtrl.rendererCtrl.elementAt(selectedSector1.centerPointIdx) as RenderVertex;
        const startPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector1.startPointIdx) as RenderVertex;
        const endPoint1 = docCtrl.rendererCtrl.elementAt(selectedSector1.endPointIdx) as RenderVertex;
        const pC1 = point(center1.coords[0], center1.coords[1]);
        const pS1 = point(startPoint1.coords[0], startPoint1.coords[1]);
        const pE1 = point(endPoint1.coords[0], endPoint1.coords[1]);
        const vecC1O = vector(pC1, point(pC1.x + 10, pC1.y));
        const angleStart1 = vecC1O.angleTo(vector(pC1, pS1));
        const angleEnd1 = vecC1O.angleTo(vector(pC1, pE1));

        const center2 = docCtrl.rendererCtrl.elementAt(selectedSector2.centerPointIdx) as RenderVertex;
        const startPoint2 = docCtrl.rendererCtrl.elementAt(selectedSector2.startPointIdx) as RenderVertex;
        const endPoint2 = docCtrl.rendererCtrl.elementAt(selectedSector2.endPointIdx) as RenderVertex;
        const pC2 = point(center2.coords[0], center2.coords[1]);
        const pS2 = point(startPoint2.coords[0], startPoint2.coords[1]);
        const pE2 = point(endPoint2.coords[0], endPoint2.coords[1]);
        const vecC2O = vector(pC2, point(pC2.x + 10, pC2.y));
        const angleStart2 = vecC2O.angleTo(vector(pC2, pS2));
        const angleEnd2 = vecC2O.angleTo(vector(pC2, pE2));

        const arc1 = arc(pC1, selectedSector1.radius, angleStart1, angleEnd1);
        const arc2 = arc(pC2, selectedSector2.radius, angleStart2, angleEnd2);

        const intersections = arc1.intersect(arc2);

        if (!intersections?.length) {
            this.resetState();
            return;
        }

        if (intersections.length === 1) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            syncPreviewCommands(pvP1, docCtrl);
            this.intersectionPreview.push(pvP1);
            await this.processBuildConstruction(docCtrl, pvP1);
            // resetState is now handled inside processBuildConstruction's finally block
            return;
        }

        if (intersections.length === 2) {
            const pvP1 = this.createPreviewVertex([intersections[0].x, intersections[0].y], -9998);
            const pvP2 = this.createPreviewVertex([intersections[1].x, intersections[1].y], -9999);

            if (
                nthDirectionRotation(
                    [center2.coords[0] - center1.coords[0], center2.coords[1] - center1.coords[1]],
                    center1.coords,
                    pvP1.coords,
                    [pvP2.coords]
                ) === 1
            ) {
                this.intersectionPreview.push(pvP1, pvP2);
            } else {
                this.intersectionPreview.push(pvP2, pvP1);
            }

            syncPreviewCommands(pvP1, docCtrl);
            syncPreviewCommands(pvP2, docCtrl);

            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async processBuildConstruction(docCtrl: GeoDocCtrl, intersectionSelected: RenderVertex) {
        const intersectionName = (
            await requestElementNames(docCtrl, this.toolbar.getTool('NamingElementTool') as NamingElementTool, [
                {
                    objName: 'Giao Điểm',
                    originElement: [intersectionSelected],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0]?.[0];
        if (!intersectionName) return;

        let construction: GeoElConstructionRequest | undefined;

        // 16 trường hợp ghép cặp giữa line, circle, ellipse, sector
        const nthFromPreview = (relIdx: number) =>
            this.intersectionPreview.length === 1
                ? undefined // For single intersection, nth is not needed or can be considered 1 by default backend logic
                : this.intersectionPreview.map(i => i.relIndex).indexOf(relIdx) + 1;

        const currentNth = nthFromPreview(intersectionSelected.relIndex);

        if (this.lines.length === 2) {
            const elA = getElementDetails(this.lines[0]);
            const elB = getElementDetails(this.lines[1]);
            construction = buildIntersectionRequest({
                cgName: 'LineLine',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                // No nth for LineLine
            });
        } else if (this.lines.length === 1 && this.circles.length === 1) {
            const elA = getElementDetails(this.lines[0]);
            const elB = getElementDetails(this.circles[0]);
            construction = buildIntersectionRequest({
                cgName: 'LineCircle',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.lines.length === 1 && this.ellipses.length === 1) {
            const elA = getElementDetails(this.lines[0]);
            const elB = getElementDetails(this.ellipses[0]);
            construction = buildIntersectionRequest({
                cgName: 'LineEllipse',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.lines.length === 1 && this.sectors.length === 1) {
            const elA = getElementDetails(this.lines[0]);
            const elB = getElementDetails(this.sectors[0]);
            construction = buildIntersectionRequest({
                cgName: 'LineSector',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.circles.length === 2) {
            const elA = getElementDetails(this.circles[0]);
            const elB = getElementDetails(this.circles[1]);
            construction = buildIntersectionRequest({
                cgName: 'CircleCircle',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.circles.length === 1 && this.ellipses.length === 1) {
            const elA = getElementDetails(this.circles[0]);
            const elB = getElementDetails(this.ellipses[0]);
            construction = buildIntersectionRequest({
                cgName: 'CircleEllipse',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.circles.length === 1 && this.sectors.length === 1) {
            const elA = getElementDetails(this.circles[0]);
            const elB = getElementDetails(this.sectors[0]);
            construction = buildIntersectionRequest({
                cgName: 'CircleSector',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.ellipses.length === 2) {
            const elA = getElementDetails(this.ellipses[0]);
            const elB = getElementDetails(this.ellipses[1]);
            construction = buildIntersectionRequest({
                cgName: 'EllipseEllipse',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.ellipses.length === 1 && this.sectors.length === 1) {
            const elA = getElementDetails(this.ellipses[0]);
            const elB = getElementDetails(this.sectors[0]);
            construction = buildIntersectionRequest({
                cgName: 'EllipseSector',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        } else if (this.sectors.length === 2) {
            const elA = getElementDetails(this.sectors[0]);
            const elB = getElementDetails(this.sectors[1]);
            construction = buildIntersectionRequest({
                cgName: 'SectorSector',
                outputName: intersectionName,
                paramA: elA,
                paramB: elB,
                nth: currentNth,
            });
        }

        if (!construction) return;

        try {
            this.intersectionConstructed.push(intersectionSelected);

            await docCtrl.editor.awarenessFeature.useAwareness(
                docCtrl.viewport.id,
                'Đang tạo giao điểm',
                buildDocumentAwarenessCmdOption(docCtrl.editor.awarenessConstructId, docCtrl),
                async () => {
                    // This callback runs with awareness shown.
                    // Errors within this async function will be caught by the outer try/catch.
                    const constructResponse = await this.editor.geoGateway.construct(docCtrl.state.globalId, [
                        {
                            construction: construction!,
                        },
                    ]);

                    await syncRenderCommands(constructResponse.render, docCtrl);
                    await addHistoryItemFromConstructionResponse(docCtrl, constructResponse);
                }
            );

            // This part runs after the awareness block completes without throwing.
            // It handles syncing previews for any *remaining* unconstructed intersection points.
            if (this.intersectionPreview.length > this.intersectionConstructed.length) {
                this.intersectionPreview
                    .filter(p => !this.intersectionConstructed.find(i => i.relIndex === p.relIndex))
                    .forEach(pvP => {
                        syncPreviewCommands(pvP, docCtrl);
                    });
            }
        } catch (e) {
            console.log(e, e instanceof AxiosError);
            if (e.response) {
                e = e as AxiosError;
                throw new GeoErr(e.response.data.toString(), e);
            } else throw new GeoErr('Có lỗi xảy ra trong quá trình tạo giao điểm', e);
        } finally {
            // Reset state regardless of success or failure
            this.resetState();
        }
    }
}
