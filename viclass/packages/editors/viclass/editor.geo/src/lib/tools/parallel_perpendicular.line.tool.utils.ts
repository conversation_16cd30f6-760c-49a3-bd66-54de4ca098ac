import { vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeoPointerEvent } from '../model/geo.models';
import { GeoSelectHitContext } from '../objects';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewVertexRenderProp,
    calculateScalingFactor,
    calculateUnitVector,
    isElementLine,
    isSamePoint,
    pickPointName,
    projectPointOntoLine,
    requestElementNames,
} from './tool.utils';

export function previewPointerMove(
    geoTool: GeometryTool<CommonToolState>,
    event: GeoPointerEvent,
    object: { point: RenderVertex; line: RenderLine },
    isPerpendicularVector: boolean = false
) {
    // if (!object.line || !object.point) return;
    // const { ctrl, pos, hitEl } = geoTool.posAndCtrl(event);
    // let mpg = [pos.x, pos.y];
    // if (hitEl?.type == 'RenderVertex') {
    //     mpg = (hitEl as RenderVertex).coords;
    // }
    // if (hitEl && isElementLine(hitEl)) {
    //     const e = hitEl as RenderLine;
    //     const pS = ctrl.rendererCtrl.elementAt(e.startPointIdx) as RenderVertex;
    //     const vS = e.vector;
    //     const pPer = object.point as RenderVertex;
    //     const vSL = object.line.vector;
    //     const vPer = isPerpendicularVector ? [vSL[1], -vSL[0], vSL[2]] : vSL;
    //     const lineS = line(point(pS.coords[0], pS.coords[1]), vector([-vS[1], vS[0]]));
    //     const linePer = line(point(pPer.coords[0], pPer.coords[1]), vector([-vPer[1], vPer[0]]));
    //     const intersectPoint = lineS.intersect(linePer)[0];
    //     if (!intersectPoint) return;
    //     mpg = [intersectPoint.x, intersectPoint.y];
    //     const pointPreview: RenderVertex = {
    //         unselectable: false,
    //         relIndex: -13,
    //         type: 'RenderVertex',
    //         elType: 'Point',
    //         renderProp: buildPreviewVertexRenderProp(),
    //         name: '',
    //         coords: mpg,
    //         usable: true,
    //         valid: true,
    //     };
    //     syncPreviewCommands(pointPreview, ctrl);
    //     return;
    // }
    // const vS = object.line.vector;
    // const v = isPerpendicularVector ? [vS[1], -vS[0], vS[2]] : vS;
    // const p = object.point.coords;
    // const [x, y] = projectPointOntoLine(mpg, p, v);
    // if (isSamePoint(mpg, [x, y], ctrl)) {
    //     const point: RenderVertex = {
    //         unselectable: true,
    //         relIndex: -13,
    //         type: 'RenderVertex',
    //         elType: 'Point',
    //         renderProp: buildPreviewVertexRenderProp(),
    //         name: '',
    //         coords: [x, y],
    //         usable: true,
    //         valid: true,
    //     };
    //     syncPreviewCommands(point, ctrl);
    // } else {
    //     const p: RenderVertex = {
    //         unselectable: true,
    //         relIndex: -13,
    //         type: 'RenderVertex',
    //         elType: 'Point',
    //         renderProp: buildPreviewVertexRenderProp(),
    //         name: '',
    //         coords: [],
    //         usable: true,
    //         valid: true,
    //     };
    //     syncPreviewCommands(p, ctrl);
    // }
}

export async function onFinalClick(
    geoTool: GeometryTool<CommonToolState>,
    event: GeoPointerEvent,
    object: {
        point: RenderVertex;
        line: RenderLine;
        lastHitCtx: GeoSelectHitContext | null;
        buildLineSegmentWithIntersectLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            intersectionLineName: string,
            intersectionLineType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
        buildLineSegment: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string,
            k: number
        ) => GeoElConstructionRequest;
        buildLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
    },
    isPerpendicularVector: boolean = false
) {
    const { ctrl, pos, hitCtx, hitEl } = geoTool.posAndCtrl(event);
    let mpg = [pos.x, pos.y];
    let intersectLine: RenderLine;

    if (hitEl) {
        ctrl.editor.selectElement(hitCtx, true);

        if (hitEl.type == 'RenderVertex') {
            const e = hitEl as RenderVertex;
            mpg = e.coords;
        }

        if (isElementLine(hitEl)) {
            intersectLine = hitEl as RenderLine;
        }
    }

    let constructionAngle = undefined;

    const startPointName = object.point.name;
    const startLineName = object.line.name;
    const startLineType = object.line.elType;

    const vS = object.line.vector;
    const v = isPerpendicularVector ? [-vS[1], vS[0]] : vS;
    const startPoint = object.point.coords;
    const projectPoint = projectPointOntoLine(mpg, startPoint, v);

    let projectPointIntersect: number[];

    if (intersectLine) {
        const vI = intersectLine.vector;
        const v1 = [-vI[1], vI[0]];
        projectPointIntersect = projectPointOntoLine(mpg, startPoint, v1);
    }

    const vertex2: RenderVertex = {
        relIndex: -12,
        type: 'RenderVertex',
        elType: 'Point',
        renderProp: buildPreviewVertexRenderProp(),
        name: undefined,
        coords: projectPoint,
        usable: true,
        valid: true,
    };

    const haveIntersectLine = !!intersectLine;
    const isPointStartOnIntersectLine = isSamePoint(object.point.coords, projectPointIntersect, ctrl);
    const isPointerAtStartPosition = isSamePoint(mpg, object.point.coords, ctrl);
    const isPointerOnPerpendicularLine = isSamePoint(mpg, projectPoint, ctrl);
    const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
    // Selected 2nd line and start point is not on 2nd line (create line segment)
    if (haveIntersectLine && !isPointStartOnIntersectLine) {
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        const intersectionLineName = intersectLine.name;
        constructionAngle = object.buildLineSegmentWithIntersectLine(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            intersectionLineName,
            intersectLine.elType,
            startPointName
        );
        // choose a point on the perpendicular line and the chosen point is not the starting point (create line segment)
    } else if (!isPointerAtStartPosition && isPointerOnPerpendicularLine) {
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        const uVector = calculateUnitVector(v);
        const k = calculateScalingFactor(uVector, startPoint, projectPoint);
        const lVector = [projectPoint[0] - startPoint[0], projectPoint[1] - startPoint[1]];

        const lineDirection =
            vector(v[0], v[1]).angleTo(vector(lVector[0], lVector[1])) * (180 / Math.PI) == 180 ? -1 : 1;

        constructionAngle = object.buildLineSegment(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            startPointName,
            k * lineDirection
        );
        // create straight line
    } else constructionAngle = object.buildLine('', startLineName, startLineType, startPointName);

    geoTool.resetState();

    await ctrl.editor.awarenessFeature.useAwareness(
        ctrl.viewport.id,
        'Đang tạo hình',
        buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        async () => {
            const constructResponse = await geoTool.editor.geoGateway.construct(ctrl.state.globalId, [
                {
                    construction: constructionAngle,
                },
            ]);

            await syncRenderCommands(constructResponse.render, ctrl);
            await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            geoTool.resetState();
        }
    );
}
