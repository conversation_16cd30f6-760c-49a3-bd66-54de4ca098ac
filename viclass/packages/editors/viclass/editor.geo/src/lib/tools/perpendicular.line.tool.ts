import {
    ErrorH<PERSON>lerDecorator,
    KeyboardHandlingItem,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, GeoRenderElement, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
import { GeometryTool } from './geo.tool';
import { onFinalClick, previewPointerMove } from './parallel_perpendicular.line.tool.utils';
import { handleIfPointerNotInError, isElementLine } from './tool.utils';

/**
 *
 * <AUTHOR>
 */
export class CreatePerpendicularLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePerpendicularLineTool';

    private point: RenderVertex;
    private line: RenderLine;
    private isPreview: boolean = false;
    private isPointerDown = false;
    private selectionStage = 0; // 0: nothing selected, 1: one element selected, 2: both elements selected

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return (
            (isElementLine(el) && !this.line) ||
            (el.type == 'RenderVertex' && !this.point) ||
            ((isElementLine(el) || el.type == 'RenderVertex') && this.point && this.line && this.isPreview)
        );
    };

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreatePerpendicularLineTool> =
        new PotentialSelectionDelegator(this);

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // pointerdown events
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', keys: ['shift'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // pointerup events
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', keys: ['shift'], button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },

            // pointermove events
            { event: 'pointermove', pointerTypes: pointerTypeMouse },
            { event: 'pointermove', keys: ['shift'], pointerTypes: pointerTypeMouse },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
        this.registerKeyboardHandling(
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keyup';
            })(['shift']),
            new (class extends KeyboardHandlingItem {
                override global = false;
                override event = 'keydown';
            })(['shift'])
        );
    }

    override resetState() {
        this.point = undefined;
        this.line = undefined;
        this.isPreview = false;
        this.isPointerDown = false;
        this.selectionStage = 0;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    private onPointerMove(event: GeoPointerEvent) {
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.isPointerDown && this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) {
            // handler pointer move
        }
        if (this.selectionStage >= 2 && this.point && this.line) {
            // When both elements are selected, show the perpendicular line preview
            const object = { point: this.point, line: this.line };
            previewPointerMove(this, event, object, true);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        if (this.selectionStage >= 2 && this.point && this.line) {
            // Already have point and line, start showing the preview for the final perpendicular line
            const object = { point: this.point, line: this.line };
            previewPointerMove(this, event, object, true);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;
        this.potentialSelectionDelegator.clearPotential();

        if (this.selectionStage < 2) {
            await this.onSelectElement(event);
        } else if (this.point && this.line) {
            await onFinalClick(
                this,
                event,
                {
                    point: this.point,
                    line: this.line,
                    lastHitCtx: this.lastHitCtx,
                    buildLineSegmentWithIntersectLine: this.buildLineSegmentWithIntersectLine,
                    buildLineSegment: this.buildLineSegment,
                    buildLine: this.buildLine,
                },
                true
            );
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onSelectElement(event: GeoPointerEvent) {
        // const { ctrl, hitCtx } = this.posAndCtrl(event);
        // if (hitCtx && hitCtx.hitDetails) {
        //     const el = (hitCtx.hitDetails as GeoHitContextDetails).el;
        //     if (!this.line && isElementLine(el)) {
        //         this.line = el as RenderLine;
        //         this.selectionStage++;
        //         ctrl.editor.selectElement(hitCtx, true);
        //     } else if (!this.point && el.type == 'RenderVertex') {
        //         this.point = el as RenderVertex;
        //         this.selectionStage++;
        //         ctrl.editor.selectElement(hitCtx, true);
        //     }
        // }
        // // Show initial preview when both line and point are selected
        // if (this.line && this.point && this.selectionStage === 2) {
        //     // 1. Compute perpendicular vector by rotating the source line vector 90°
        //     const vS = this.line.vector;
        //     const vPer = [vS[1], -vS[0], vS[2]];
        //     // 2. Build a PreviewLine anchored at the selected point in that direction
        //     const linePreview: PreviewLine = {
        //         unselectable: true,
        //         relIndex: -30,
        //         type: 'RenderLine',
        //         elType: 'LineVi',
        //         name: '',
        //         renderProp: buildPreviewLineRenderProp(),
        //         startPoint: this.point.coords,
        //         endPoint: this.point.coords,
        //         vector: [...vPer],
        //         usable: true,
        //         valid: true,
        //     };
        //     // 3. Activate preview mode and sync it to the renderer
        //     this.isPreview = true;
        //     syncPreviewCommands(linePreview, ctrl);
        // }
    }

    private buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'ThoughPointPerpendicularWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'PerpendicularWithNewPoint'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegmentWithIntersectLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        intersectionLineName: string,
        intersectionLineType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'PerpendicularWithIntersectLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
