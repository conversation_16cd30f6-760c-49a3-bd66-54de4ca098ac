import { point } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption, ErrorHandlerDecorator, UIPointerEventData } from '@viclass/editor.core';
import { syncEndPreviewModeCommand, syncRemovePreviewCmd, syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    LineGeoRenderProp,
    RenderLineSegment,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { GeoRenderer } from '../renderer';
import { vertexOnStroke, VertexOnStrokeSelector } from '../selectors';
import { constructExec, GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    assignNames,
    buildPreviewLineRenderProp,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    pointOnElAsParam,
} from './tool.utils';

type ConstructionArg = {
    name: string;
    pointName: string;
    numberParam: number;
    constructionType:
        | 'Point/PointOnEllipseEC'
        | 'Point/PointOnCircleEC'
        | 'Point/PointOnCircularSectorEC'
        | 'Point/PointOnLineEC';
    elType: 'Ellipse' | 'Circle' | 'CircularSector' | 'LineSegment' | 'Line';
    paramType: 'anEllipse' | 'aCircle' | 'aCircularSector' | 'aLineSegment' | 'aLine';
    paramTpl: 'tpl-OnEllipse' | 'tpl-OnCircle' | 'tpl-OnCircularSector' | 'tpl-OnLineSegment' | 'tpl-OnLine';
    numberTpl: 'tpl-AngleRadian' | 'tpl-RatioValue' | 'tpl-CoefficientIs';
    numberParamName?: string; // 'ratio' or 'coefficient' hoặc 'angle'
    numberValueKey?: string; // default: value
};

const snapPos = [0.1, 0.2, 0.25, 0.3, 0.33333333, 0.4, 0.5, 0.6, 0.66666666, 0.7, 0.8, 0.9];
export class PointOnObjectTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'PointOnObjectTool';

    declare selLogic?: VertexOnStrokeSelector;
    private pQ: PreviewQueue = new PreviewQueue();

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();

        this.selLogic = vertexOnStroke({
            previewQueue: this.pQ,
            syncPreview: true,
            cursor: this.pointerHandler.cursor,
            onComplete: this.performConstruction.bind(this),
        });
    }

    override resetState() {
        this.selLogic?.reset();
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    async performConstruction(selector: VertexOnStrokeSelector, ctrl: GeoDocCtrl) {
        const { pcs } = await assignNames(
            ctrl,
            [selector.selected],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool
        );

        if (!pcs) {
            this.resetState();
            return;
        }
        try {
            // Since awareness is a common feature, it is not optional
            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo điểm',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    try {
                        const constructResponse = await constructExec(() =>
                            this.editor.geoGateway.construct(ctrl.state.globalId, [{ construction: pcs[0] }])
                        );

                        await syncRenderCommands(constructResponse.render, ctrl);
                        await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                    } catch (e) {
                        await syncEndPreviewModeCommand(ctrl);
                        throw e;
                    }
                }
            );
        } finally {
            this.resetState();
        }
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        let previews: StrokeType[] = [];

        if (selected && selected.length == 2) {
            const vertex = selected[1];
            const el = selected[0];
            switch (el.type) {
                case 'RenderLineSegment': // show current ratio
                case 'RenderVector': {
                    const s = el.coord('start', ctrl.rendererCtrl);
                    const e = el.coord('end', ctrl.rendererCtrl);
                    const ps = point(s[0], s[1]);
                    const pe = point(e[0], e[1]);
                    const pp = point(vertex.coords[0], vertex.coords[1]);
                    const l = ps.distanceTo(pe)[0];
                    const r1 = pp.distanceTo(ps)[0] / l;
                    const r2 = pp.distanceTo(pe)[0] / l;

                    if (s && e) {
                        const l1 = pLine(ctrl, -20, RenderLineSegment, s, vertex.coords);
                        l1.renderProp = Object.assign(buildPreviewLineRenderProp(), <Partial<LineGeoRenderProp>>{
                            lineColor: '#ff0000',
                            showLabel: true,
                            label: `${Math.round(r1 * 1000) / 1000}`,
                            strokeStyle: 'Solid',
                        });
                        const l2 = pLine(ctrl, -21, RenderLineSegment, e, vertex.coords);
                        l2.renderProp = Object.assign(buildPreviewLineRenderProp(), <Partial<LineGeoRenderProp>>{
                            lineColor: '#ff0000',
                            showLabel: true,
                            label: `${Math.round(r2 * 1000) / 1000}`,
                            strokeStyle: 'Solid',
                        });
                        previews = [l1, l2];
                    }
                    break;
                }
            }
        }

        const tobeRemovedPreviews = [-20, -21].filter(
            id => !previews.map(p => p.relIndex).includes(id) && ctrl.rendererCtrl.previewElAt(id)
        );
        if (tobeRemovedPreviews.length > 0) syncRemovePreviewCmd(tobeRemovedPreviews, ctrl);

        previews.forEach(p => this.pQ.add(p));
        this.pQ.flush(ctrl);
    }
}

export function buildPointOnElConstruction(
    vertex: RenderVertex,
    el: StrokeType,
    renderer: GeoRenderer
): GeoElConstructionRequest {
    let ctReq: GeoElConstructionRequest;

    const paramK = pointOnElAsParam(el, [vertex.coords[0], vertex.coords[1]], renderer);
    const ctFrag = {
        name: el.name,
        numberParam: paramK,
        pointName: vertex.name,
    };
    switch (el.type) {
        case 'RenderLineSegment':
        case 'RenderVector':
            ctReq = buildConstructionHelper({
                ...ctFrag,
                constructionType: 'Point/PointOnLineEC',
                elType: 'LineSegment',
                paramType: 'aLineSegment',
                paramTpl: 'tpl-OnLineSegment',
                numberTpl: 'tpl-RatioValue',
            });
            break;
        case 'RenderLine':
        case 'RenderRay':
            ctReq = buildConstructionHelper({
                ...ctFrag,
                constructionType: 'Point/PointOnLineEC',
                elType: 'Line',
                paramType: 'aLine',
                paramTpl: 'tpl-OnLine',
                numberTpl: 'tpl-CoefficientIs',
            });
            break;
        case 'RenderSector':
            ctReq = buildConstructionHelper({
                ...ctFrag,
                constructionType: 'Point/PointOnCircularSectorEC',
                elType: 'CircularSector',
                paramType: 'aCircularSector',
                paramTpl: 'tpl-OnCircularSector',
                numberTpl: 'tpl-AngleRadian',
            });
            break;
        case 'RenderCircle':
            ctReq = buildConstructionHelper({
                ...ctFrag,
                constructionType: 'Point/PointOnCircleEC',
                elType: 'Circle',
                paramType: 'aCircle',
                paramTpl: 'tpl-OnCircle',
                numberTpl: 'tpl-AngleRadian',
            });
            break;
        case 'RenderEllipse':
            ctReq = buildConstructionHelper({
                ...ctFrag,
                constructionType: 'Point/PointOnEllipseEC',
                elType: 'Ellipse',
                paramType: 'anEllipse',
                paramTpl: 'tpl-OnEllipse',
                numberTpl: 'tpl-AngleRadian',
            });
            break;
    }

    return ctReq;
}

function buildConstructionHelper(arg: ConstructionArg): GeoElConstructionRequest {
    const {
        name,
        pointName,
        numberParam,
        constructionType,
        elType,
        paramType,
        paramTpl,
        numberTpl,
        numberParamName = 'value',
    } = arg;
    const construction = new GeoElConstructionRequest(constructionType, 'Point', getCGAction(constructionType, elType));

    construction.name = pointName;

    construction.paramSpecs = [
        {
            indexInCG: 0,
            paramDefId: paramType,
            optional: false,
            tplStrLangId: paramTpl,
            params: {
                name: {
                    type: 'singleValue',
                    value: name,
                },
            },
        },
        {
            indexInCG: 1,
            paramDefId: 'aValue',
            optional: false,
            tplStrLangId: numberTpl,
            params: {
                [numberParamName]: {
                    type: 'singleValue',
                    value: numberParam,
                },
            },
        },
    ];
    return construction;
}
// Helper to map constructionType & elType to CG action string
function getCGAction(constructionType: string, elType: string): string {
    if (constructionType === 'Point/PointOnEllipseEC') return 'OnEllipseWithRadian';
    if (constructionType === 'Point/PointOnCircleEC') return 'OnCircleWithRadian';
    if (constructionType === 'Point/PointOnCircularSectorEC') return 'OnCircularSectorWithRadian';
    if (constructionType === 'Point/PointOnLineEC') {
        if (elType === 'LineSegment') return 'OnLineSegmentWithRatio';
        if (elType === 'Line') return 'OnLineWithCoefficient';
    }
    return '';
}
