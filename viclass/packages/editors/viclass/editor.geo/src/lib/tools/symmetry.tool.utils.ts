import { GeoRelType, GeoRenderElement, RenderVertex } from '../model';
import { GeoDocCtrl } from '../objects';
import { <PERSON><PERSON><PERSON>ender<PERSON> } from '../renderer';
import { projectPointOntoLine } from './tool.utils';

export function symmetricPoint(originalPoint: number[], middlePoint: number[]): number[] {
    if (!originalPoint) return [];
    return [2 * middlePoint[0] - originalPoint[0], 2 * middlePoint[1] - originalPoint[1]];
}

export function calculateSymmetricPointsThroughLine(
    startPointOfLine: number[],
    vector?: number[]
): (params: number[][]) => number[][] {
    return (originalPoints: number[][]) =>
        originalPoints.map(originalPoint => {
            const pointInLine = projectPointOntoLine(originalPoint, startPointOfLine, vector);
            return symmetricPoint(originalPoint, pointInLine);
        });
}

export function calculateSymmetricPointsThroughMiddlePoint(middlePoint: number[]): (params: number[][]) => number[][] {
    return (originalPoints: number[][]): number[][] =>
        originalPoints.map(originalPoint => {
            return symmetricPoint(originalPoint, middlePoint);
        });
}

function removePreview(preview: GeoRenderElement, ctrl: GeoDocCtrl): Function {
    throw new Error('Function not implemented.');
    // return () => {
    //     if (!preview) return;

    //     const previewRemoveData: GeoPreviewElement<any> = {
    //         ...preview,
    //         usable: false,
    //         valid: false,
    //         unselectable: false,
    //     };
    //     syncPreviewCommands(previewRemoveData, ctrl);
    // };
}

export function createSymmetry(
    element: GeoRenderElement,
    ctrl: GeoDocCtrl,
    renderer: GeoRenderer,
    calculateSymmetricPoints: (params: number[][]) => number[][],
    opposite?: GeoRelType[]
): Function | null {
    // if (!element) return null;

    // if (element.type === 'RenderVertex') {
    //     const pointPosition = (element as RenderVertex).coords;
    //     const symmetricPoint = calculateSymmetricPoints([pointPosition]);
    //     const previewVertex = createPreviewVertex(symmetricPoint[0]);
    //     syncPreviewCommands(previewVertex, ctrl);
    //     return removePreview(previewVertex, ctrl);
    // }

    // if (element.type === 'RenderPolygon') {
    //     const polygon = element as RenderPolygon;
    //     const pointPositions = polygon.faces.map(face => (renderer.elementAt(face) as RenderVertex).coords);
    //     const symmetricPolygon = calculateSymmetricPoints(pointPositions);
    //     const previewPolygon = createPreviewPolygon(symmetricPolygon);
    //     syncPreviewCommands(previewPolygon, ctrl);
    //     return removePreview(previewPolygon, ctrl);
    // }

    // if (isElementLine(element)) {
    //     const line = element as RenderLineSegment | RenderRay | RenderVector;
    //     const endPointOfLineStart = renderer.elementAt(line.endPointIdx) as RenderVertex;
    //     const startPointOfLineStart = renderer.elementAt(line.startPointIdx) as RenderVertex;
    //     const pointArray = [startPointOfLineStart.coords, endPointOfLineStart.coords];
    //     const symmetricPoints = calculateSymmetricPoints(pointArray);
    //     const previewLine = createPreviewLine(symmetricPoints, line.type, line.elType);
    //     syncPreviewCommands(previewLine, ctrl);
    //     return removePreview(previewLine, ctrl);
    // }

    // if (element.type === 'RenderCircleShape') {
    //     const circle = element as RenderCircleShape;
    //     const centerPoint = renderer.elementAt(circle.centerPointIdx) as RenderVertex;
    //     const symmetricPoint = calculateSymmetricPoints([centerPoint.coords]);
    //     const previewCircle = createPreviewCircle(symmetricPoint[0], circle.radius);
    //     syncPreviewCommands(previewCircle, ctrl);
    //     return removePreview(previewCircle, ctrl);
    // }

    // if (element.type === 'RenderEllipseShape') {
    //     const ellipse = element as RenderEllipseShape;
    //     const f1 = renderer.elementAt(ellipse.f1Idx) as RenderVertex;
    //     const f2 = renderer.elementAt(ellipse.f2Idx) as RenderVertex;
    //     const symmetricPoints = calculateSymmetricPoints([f1.coords, f2.coords]);
    //     const previewEllipse = createPreviewEllipse(symmetricPoints, ellipse.a, ellipse.b);
    //     syncPreviewCommands(previewEllipse, ctrl);
    //     return removePreview(previewEllipse, ctrl);
    // }

    // if (element.type === 'RenderSectorShape') {
    //     const circularSector = element as RenderSectorShape;
    //     const centerPoint = renderer.elementAt(circularSector.centerPointIdx) as RenderVertex;
    //     const startPoint = renderer.elementAt(circularSector.startPointIdx) as RenderVertex;
    //     const endPoint = renderer.elementAt(circularSector.endPointIdx) as RenderVertex;
    //     let pointArray = [centerPoint?.coords, startPoint?.coords, endPoint?.coords];

    //     if (isOpposite(opposite, element)) pointArray = [centerPoint?.coords, endPoint?.coords, startPoint?.coords];

    //     const symmetricPoints = calculateSymmetricPoints([...pointArray]);
    //     const previewCircularSector = createPreviewCircularSector(symmetricPoints, element.elType);
    //     syncPreviewCommands(previewCircularSector, ctrl);
    //     return removePreview(previewCircularSector, ctrl);
    // }
    return null;
}

function isOpposite(opposite: GeoRelType[], element: GeoRenderElement) {
    return opposite && opposite.find(x => x === element.type);
}

function createPreviewVertex(coords: number[]): RenderVertex {
    throw new Error('Function not implemented.');
    // return {
    //     type: 'RenderVertex',
    //     elType: 'Point',
    //     relIndex: -10,
    //     coords,
    //     name: '',
    //     renderProp: buildPreviewVertexRenderProp(),
    //     usable: true,
    //     valid: true,
    //     unselectable: true,
    // };
}

// function createPreviewPolygon(faces: number[][]): PreviewPolygon {
//     return {
//         relIndex: -19,
//         name: '',
//         type: 'RenderPolygon',
//         elType: 'Polygon',
//         verts: faces,
//         renderProp: buildPreviewPolygonRenderProp(),
//         usable: true,
//         valid: true,
//     };
// }

// function createPreviewLine(
//     points: number[][],
//     type: GeoRelType,
//     elType: GeoObjectType
// ): PreviewLine | PreviewLineSegment | PreviewRay | PreviewVector {
//     return {
//         relIndex: -25,
//         renderProp: buildPreviewLineSegmentRenderProp(),
//         name: '',
//         type: type,
//         elType: elType,
//         startPoint: points[0],
//         endPoint: points[1],
//         usable: true,
//         valid: true,
//     };
// }

// function createPreviewCircle(centerPoint: number[], radius: number): PreviewCircleShape {
//     return {
//         relIndex: -20,
//         type: 'RenderCircleShape',
//         elType: 'Circle',
//         centerPoint,
//         radius,
//         name: '',
//         renderProp: buildPreviewCircleShapeRenderProp(),
//         usable: true,
//         valid: true,
//     };
// }

// function createPreviewEllipse(points: number[][], a: number, b: number): PreviewEllipseShape {
//     let rotate;
//     if (!points || points.length === 0) rotate = 0;
//     else rotate = vector(1, 0).angleTo(vector(point(points[0][0], points[0][1]), point(points[1][0], points[1][1])));

//     return {
//         relIndex: -21,
//         type: 'RenderEllipseShape',
//         elType: 'Ellipse',
//         f1: points[0],
//         f2: points[1],
//         a,
//         b,
//         rotate: rotate,
//         name: '',
//         renderProp: buildPreviewCircleShapeRenderProp(),
//         usable: true,
//         valid: true,
//     };
// }

// function createPreviewCircularSector(points: number[][], elType: GeoObjectType): PreviewSectorShape {
//     return {
//         relIndex: -22,
//         name: '',
//         type: 'RenderSectorShape',
//         elType: elType,
//         renderProp: buildPreviewSectorShapeRenderProp(),
//         usable: true,
//         valid: true,
//         centerPoint: points[0],
//         startPoint: points[1],
//         endPoint: points[2],
//     };
// }
