<div class="regpolygon-edge-input-overlay shadow-SH1 gap-1">
    <div class="flex justify-center items-center gap-[20px]">
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': (sod$ | async) == 'side' }"
            (click)="setSOD('side')">
            Side
        </button>
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': (sod$ | async) == 'diagonal' }"
            (click)="setSOD('diagonal')">
            Diagonal
        </button>
    </div>
</div>
