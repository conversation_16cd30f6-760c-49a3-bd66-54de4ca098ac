import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import { GeometryTool, GeometryToolBar, GeometryToolType, QuadToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    selector: 'tb-quad-sod',
    templateUrl: './quadrilateral-sod.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QuadSoDComponent implements AfterViewInit, OnDestroy {
    private toolListener = QuadSoDComponent.ToolListener(this);

    public sod$ = new BehaviorSubject<'side' | 'diagonal'>('side');

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {}

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setSOD(value: 'side' | 'diagonal') {
        const ts = this.toolbar.toolState(this.tooltype) as QuadToolState;
        ts.sideOrDiagonal = value;
        this.toolbar.update(this.tooltype, ts);
    }

    get sod(): 'side' | 'diagonal' {
        const ts = this.toolbar.toolState(this.tooltype) as QuadToolState;
        return ts.sideOrDiagonal;
    }

    private updateInputFromToolState() {
        this.sod$.next(this.sod);
    }

    private static ToolListener(
        _p: QuadSoDComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if (eventData.toolType != _p.tooltype) return eventData;

                _p.updateInputFromToolState();
                _p.changeDetectorRef.markForCheck();

                return eventData;
            }
        })();
    }
}
