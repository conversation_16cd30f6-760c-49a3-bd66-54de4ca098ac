<div class="regpolygon-edge-input-overlay shadow-SH1 gap-1">
    <div class="flex justify-center items-center gap-2">
        <label for="regpolygon-edge-input-overlay__input">
            <PERSON><PERSON> cạnh
            <input
                type="number"
                id="regpolygon-edge-input-overlay__input"
                min="3"
                max="8"
                [value]="inputValue$ | async"
                (input)="onRegularPolygonChangeNoEdge($event)"
                class="form-control"
                [ngClass]="{
                    invalid: !(isInputValid$ | async),
                }" />
        </label>
        <button type="button" class="vi-btn vi-btn-small vi-btn-focus" (click)="rotateNextPreviewPoint()">Xoay</button>
    </div>
    <div *ngIf="!(isInputValid$ | async)" class="text-SC5 text-center text-[10px]"><PERSON><PERSON><PERSON> tr<PERSON> hợp lệ ph<PERSON>i từ 3 đến 8</div>
</div>
