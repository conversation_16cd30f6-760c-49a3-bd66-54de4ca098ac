import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import {
    CreateRegularPolygonTool,
    GeometryTool,
    GeometryToolBar,
    GeometryToolType,
    RegularPolygonToolState,
} from '@viclass/editor.geo';
import { BehaviorSubject, Subscription } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    selector: 'tb-regular-polygon-edges-input',
    templateUrl: './regular-polygon-edges-input.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegularPolygonEdgesInputComponent implements OnInit, OnDestroy {
    private toolClickSubscription: Subscription;
    private toolListener = RegularPolygonEdgesInputComponent.ToolListener(this);

    readonly inputValue$ = new BehaviorSubject<string>('');
    readonly isInputValid$ = new BehaviorSubject<boolean>(true);
    regularPolygonTool: CreateRegularPolygonTool;

    constructor(
        @Inject(TOOLBAR) private toolBar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType,
        private changeDetectorRef: ChangeDetectorRef
    ) {}

    ngOnInit(): void {
        this.regularPolygonTool = this.toolBar.getTool(this.tooltype) as CreateRegularPolygonTool;
        this.toolBar.registerToolListener(this.toolListener);

        // this.inputValue$.next(this.regularPolygonTool.noEdge.toString());
    }

    ngOnDestroy(): void {
        this.toolClickSubscription?.unsubscribe();
        this.toolBar.unregisterToolListener(this.toolListener);
    }

    onRegularPolygonChangeNoEdge(event: any) {
        this.inputValue$.next(event.target.value);

        this.regularPolygonTool.noEdge = parseInt(event.target.value);
    }

    get noEdge(): number {
        const toolState = this.toolBar.toolState('CreateRegularPolygonTool') as RegularPolygonToolState;
        return toolState.noEdge;
    }

    /**
     * TODO, validation at UI is enough.
     * Another validation should be done when construction on server.
     * Communicating between tool and UI for range constraint is overkill
     */
    private updateInputFromToolState() {
        const tool = this.regularPolygonTool;

        // this.isInputValid$.next(tool.isValid);
        // if (tool.isValid) this.inputValue$.next(tool.noEdge.toString());
    }

    rotateNextPreviewPoint() {}

    private static ToolListener(
        _p: RegularPolygonEdgesInputComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if (eventData.toolType == _p.tooltype) {
                    _p.updateInputFromToolState();
                    _p.changeDetectorRef.markForCheck();
                }
                return eventData;
            }
        })();
    }
}
