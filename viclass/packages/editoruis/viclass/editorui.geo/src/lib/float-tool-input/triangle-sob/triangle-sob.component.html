<div class="regpolygon-edge-input-overlay shadow-SH1 gap-1">
    <div class="flex justify-center items-center gap-[20px]">
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': !(sob$ | async) }"
            (click)="setSOB(false)">
            Side
        </button>
        <button
            type="button"
            class="vi-btn vi-btn-small"
            [ngClass]="{ 'vi-btn-focus': sob$ | async }"
            (click)="setSOB(true)">
            Base
        </button>
    </div>
</div>
