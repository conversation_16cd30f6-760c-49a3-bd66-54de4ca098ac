syntax = "proto3";

package viclass.proto.geometry.cmd;

import "geo_msg.proto";

enum CmdTypeProto {
  DUMMY = 0;
  UPDATE_DOC_STATE = 4;

  RENDER_VERTEX = 20;

  RENDER_LINE = 22;
  PREVIEW_LINE = 23;

  RENDER_POLYGON = 24;
  PREVIEW_POLYGON = 25;

  RENDER_CIRCLE_SHAPE = 26;
  PREVIEW_CIRCLE_SHAPE = 27;

  RENDER_ELLIPSE_SHAPE = 28;
  PREVIEW_ELLIPSE_SHAPE = 29;

  RENDER_CIRCULAR_SECTOR_SHAPE = 30;
  PREVIEW_CIRCULAR_SECTOR_SHAPE = 31;

  RENDER_ANGLE = 32;
  PREVIEW_ANGLE = 33;

  RENDER_SECTOR = 35;
  PREVIEW_SECTOR = 36;

  RENDER_ELLIPSE = 37;
  PREVIEW_ELLIPSE = 38;

  RENDER_CIRCLE = 41;
  PREVIEW_CIRCLE = 42;

  UPDATE_EL_NAME = 100;
  UPDATE_ELS_PROP = 110;

  HIGHLIGHT_ELEMENTS = 200;
  REMOVE_HIGHLIGHT_ELEMENTS = 210;

  POTENTIAL_SELECTION_ELEMENTS = 300;
  REMOVE_POTENTIAL_SELECTION_ELEMENTS = 310;

  REMOVE_PREVIEW_BY_REL_IDS = 400;

  END_PREVIEW = 1000;

  UPDATE_DEFAULT_EL_RENDER_PROPS = 2000;
}

message UpdateElsPropCmdProto {
  repeated int32 rel_index = 1;
  geometry.msg.ElRenderPropsProto el_render_props = 2;
}

message RemovePreviewElsCmdProto {
    repeated int32 rel_ids = 1;
}

message UpdateDocDefaultElRenderPropsCmdProto {
  string global_id = 1;
  geometry.msg.DocDefaultElRenderPropsProto doc_default_el_render_props = 2;
}

message UpdateElNameCmdProto {
  int32 rel_index = 1;
  string name = 2;
}

message HighlightElementsCmdProto {
  int32 layer_id = 1;
  repeated int32 rel_index = 2;
}

message RemoveHighlightElementsCmdProto {
  int32 layer_id = 1;
  repeated int32 rel_index = 2;
}

message EndPreviewCmdProto {
  int32 layer_id = 1;
}

message InsertDocCmdProto {
  string global_id = 1;
  geometry.msg.BoundaryProto boundary = 2;
  geometry.msg.GeoKind kind = 3;
  uint32 unit = 4;
  double zoom_level = 5;
  geometry.msg.PositionProto look_at = 6;
  geometry.msg.PositionProto rotation = 7;
}

message UpdateDocStateCmdProto {
  string global_id = 1;
  geometry.msg.DocRenderPropProto doc_render_prop = 6;
}

message InsertLayerCmdProto {
  geometry.msg.BoundaryProto boundary = 1;
  int32 z_index = 2;
}

message NewDocBoundaryCmdProto {
  geometry.msg.BoundaryProto boundary = 1;
}

/**
 * render geometry object
 */

message RenderObjectsCmdProto {
  int32 layer_id = 2;
}

message RenderVertexCmdProto {
  geometry.msg.RenderVertexProto vertex = 1;
  int32 layer_id = 2;
}

message RenderLineCmdProto {
  geometry.msg.RenderLineProto line = 1;
  int32 layer_id = 2;
}
message PreviewLineCmdProto {
  geometry.msg.PreviewLineProto line = 1;
  int32 layer_id = 2;
}

message RenderLineSegmentCmdProto {
  geometry.msg.RenderLineProto line = 1;
  int32 layer_id = 2;
}

message RenderPolygonCmdProto {
  geometry.msg.RenderPolygonProto polygon = 1;
  int32 layer_id = 2;
}
message PreviewPolygonCmdProto {
  geometry.msg.PreviewPolygonProto polygon = 1;
  int32 layer_id = 2;
}

message RenderCircleShapeCmdProto {
  geometry.msg.RenderCircleShapeProto circle = 1;
  int32 layer_id = 2;
}
message PreviewCircleShapeCmdProto {
  geometry.msg.PreviewCircleShapeProto circle = 1;
  int32 layer_id = 2;
}

message RenderSectorShapeCmdProto {
  geometry.msg.RenderSectorShapeProto sector = 1;
  int32 layer_id = 2;
}
message PreviewSectorShapeCmdProto {
  geometry.msg.PreviewSectorShapeProto sector = 1;
  int32 layer_id = 2;
}

message RenderSectorCmdProto {
  geometry.msg.RenderSectorProto arc = 1;
  int32 layer_id = 2;
}
message PreviewSectorCmdProto {
  geometry.msg.PreviewSectorProto arc = 1;
  int32 layer_id = 2;
}

message RenderEllipseCmdProto {
  geometry.msg.RenderEllipseProto arc = 1;
  int32 layer_id = 2;
}
message PreviewEllipseCmdProto {
  geometry.msg.PreviewEllipseProto arc = 1;
  int32 layer_id = 2;
}

message RenderCircleCmdProto {
  geometry.msg.RenderCircleProto arc = 1;
  int32 layer_id = 2;
}
message PreviewCircleCmdProto {
  geometry.msg.PreviewCircleProto arc = 1;
  int32 layer_id = 2;
}

message RenderEllipseShapeCmdProto {
  geometry.msg.RenderEllipseShapeProto ellipse = 1;
  int32 layer_id = 2;
}
message PreviewEllipseShapeCmdProto {
  geometry.msg.PreviewEllipseShapeProto ellipse = 1;
  int32 layer_id = 2;
}

message RenderAngleCmdProto {
  geometry.msg.RenderAngleProto angle = 1;
  int32 layer_id = 2;
}
message PreviewAngleCmdProto {
  geometry.msg.PreviewAngleProto angle = 1;
  int32 layer_id = 2;
}
